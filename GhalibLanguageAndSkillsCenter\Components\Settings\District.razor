﻿@page "/district"
@using System.ComponentModel.DataAnnotations
@inject IGenericRepository<DistrictModel> districtRepository
@inject IGenericRepository<ProvinceModel> provinceRepository
@rendermode InteractiveServer
@attribute [Authorize]

<h3>District</h3>

<EditForm Model="@newDistrict" OnValidSubmit="HandleSubmit" FormName="district">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label for="districtName">English Name</label>
        <InputText id="districtName" class="form-control" @bind-Value="newDistrict.District" />
    </div>

    <div class="form-group">
        <label for="dariName">Dari Name</label>
        <InputText id="dariName" class="form-control" @bind-Value="newDistrict.DistrictDari" />
    </div>

    <div class="form-group">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newDistrict.DistrictPashto" />
    </div>

    <div class="form-group">
        <label for="provinceSelect">Province</label>
        <InputSelect id="provinceSelect" class="form-control" @bind-Value="newDistrict.ProvinceId">
            <option value="0">-- Select Province --</option>
            @foreach (var prov in provinces)
            {
                <option value="@prov.ProvinceId">@prov.Province</option>
            }
        </InputSelect>
    </div>

    <button type="submit" class="btn btn-primary mt-2">
        @(isEditMode ? "Update District" : "Add District")
    </button>
    @if (isEditMode)
    {
        <button type="button" class="btn btn-secondary mt-2 ms-2" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

@if (isSuccess)
{
    <p class="alert alert-success mt-2">Operation completed successfully!</p>
}
@if (isError)
{
    <p class="alert alert-danger mt-2">Error: @errorMessage</p>
}

<hr />

<h3>Districts List</h3>
@if (districts.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English</th>
                <th>Dari</th>
                <th>Pashto</th>
                <th>Province</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var dist in districts)
            {
                <tr>
                    <td>@dist.DistrictId</td>
                    <td>@dist.District</td>
                    <td>@dist.DistrictDari</td>
                    <td>@dist.DistrictPashto</td>
                    <td>@provinces.FirstOrDefault(p => p.ProvinceId == dist.ProvinceId)?.Province</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditDistrict(dist)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(dist.DistrictId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No districts found.</p>
}


<DeleteModal IsVisible="@ShowModal" IsVisibleChanged="@((value)=>ShowModal = value)" OnDeleteConfirmed="@onDeleteConfirm" />

@code {
    private DistrictModel newDistrict = new DistrictModel();
	private bool ShowModal = false;
    private List<DistrictModel> districts = new();
    private List<ProvinceModel> provinces = new();
    private bool isEditMode = false;
    private bool isSuccess = false;
    private bool isError = false;
    private string errorMessage = string.Empty;

    private int districtToDelete;

    protected override async Task OnInitializedAsync()
    {
        provinces = (await provinceRepository.GetAllAsync("usp_GetAllProvinces")).ToList();
        districts = (await districtRepository.GetAllAsync("usp_GetAllDistricts")).ToList();
    }

	private async Task onDeleteConfirm(bool confirmed)
	{
		if (confirmed)
		{
			await DeleteDistrict();
			ShowModal = false;
		}
		else
		{
			ShowModal = false;
		}
	}
    private async Task HandleSubmit()
    {
        if (newDistrict.ProvinceId == 0)
        {
            isError = true;
            errorMessage = "Please select a province.";
            return;
        }

        try
        {
            if (isEditMode)
            {
                await districtRepository.UpdateAsync("usp_UpdateDistrict", new
                {
                    newDistrict.DistrictId,
                    newDistrict.District,
                    newDistrict.DistrictPashto,
                    newDistrict.DistrictDari,
                    newDistrict.ProvinceId
                });
            }
            else
            {
                await districtRepository.AddAsync("usp_CreateDistrict", new
                {
                    newDistrict.District,
                    newDistrict.DistrictPashto,
                    newDistrict.DistrictDari,
                    newDistrict.ProvinceId
                });
            }

            isSuccess = true;
            isError = false;
            newDistrict = new DistrictModel();
            isEditMode = false;
            districts = (await districtRepository.GetAllAsync("usp_GetAllDistricts")).ToList();
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = ex.Message;
        }
    }

    private void EditDistrict(DistrictModel dist)
    {
        isEditMode = true;
        newDistrict = new DistrictModel
            {
                DistrictId = dist.DistrictId,
                District = dist.District,
                DistrictPashto = dist.DistrictPashto,
                DistrictDari = dist.DistrictDari,
                ProvinceId = dist.ProvinceId
            };
    }

    private void ConfirmDelete(int id)
    {
		ShowModal = true;
        districtToDelete = id;
    }

    private async Task DeleteDistrict()
    {
        try
        {
            await districtRepository.DeleteAsync("usp_DeleteDistrict", new { DistrictId = districtToDelete });
            
            isSuccess = true;
            districts = (await districtRepository.GetAllAsync("usp_GetAllDistricts")).ToList();
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = ex.Message;
        }
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newDistrict = new DistrictModel();
    }

  

    public class DistrictModel
    {
        public int DistrictId { get; set; }
        [Required(ErrorMessage = "Please enter English name.")] public string District { get; set; }
        public string DistrictPashto { get; set; }
        public string DistrictDari { get; set; }
        [Required(ErrorMessage = "Please select a province.")] public int ProvinceId { get; set; }
    }

    public class ProvinceModel
    {
        public short ProvinceId { get; set; }
        public string Province { get; set; }
    }
}
