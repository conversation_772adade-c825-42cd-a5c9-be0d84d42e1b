﻿@page "/staff"
@inject IGenericRepository<StaffModel> staffRepository
@inject IGenericRepository<PositionModel> positionRepository
@rendermode InteractiveServer
@inject NavigationManager NavigationManager
@attribute [Authorize]


@if (!string.IsNullOrEmpty(messages.Message))
{

    <SnackbarMessages Message="@messages.Message" Type="@messages.Type" />
}
<EditForm Model="@newStaff" OnValidSubmit="HandleSubmit" FormName="staff" class="shadow-lg p-4">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label>Name</label>
        <InputText class="form-control" @bind-Value="newStaff.Name" />
    </div>

    <div class="form-group">
        <label>Last Name</label>
        <InputText class="form-control" @bind-Value="newStaff.LastName" />
    </div>

    <div class="form-group">
        <label>Father Name</label>
        <InputText class="form-control" @bind-Value="newStaff.FatherName" />
    </div>

    <div class="form-group">
        <label>Age</label>
        <InputNumber class="form-control" @bind-Value="newStaff.Age" />
    </div>

    <div class="form-group">
        <label>Select Position</label>
        <InputSelect class="form-control" @bind-Value="newStaff.PositionId">
            <option value="">-- Select Position --</option>
            @foreach (var position in positions)
            {
                <option value="@position.PositionId">@position.EnglishName</option>
            }
        </InputSelect>
    </div>

        <div class="form-group">
            <label>Profile Image</label>
            <InputFile OnChange="HandleProfileImageUpload"  class="form-control"/>
        </div>


    <button class="btn btn-primary mt-2" type="submit">
       Add
    </button>

  
</EditForm>

<hr />

<h3>Staff List</h3>

@if (staffList is not null && staffList.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>Name</th>
                <th>Last Name</th>
                <th>Father Name</th>
                <th>Age</th>
                <th>Position</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var staff in staffList)
            {
                <tr>
                    <td>@staff.StaffId</td>
                    <td>@staff.Name</td>
                    <td>@staff.LastName</td>
                    <td>@staff.FatherName</td>
                    <td>@staff.Age</td>
                    <td>@(positions.FirstOrDefault(p => p.PositionId == staff.PositionId)?.EnglishName)</td>
                    <td>
                        <button class="btn btn-info btn-sm" @onclick="() => GoToDetails(staff.StaffId)">See Details</button>
						
                      
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(staff.StaffId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No staff found.</p>
}
<DeleteModal IsVisible="@showModal" IsVisibleChanged="@((value)=>showModal = value)" OnDeleteConfirmed="@onDeleteConfirm"/>

@code {
	private Messages messages = new();
    private StaffModel newStaff = new();
    private List<StaffModel> staffList = new();
    private List<PositionModel> positions = new();
    private bool isEditMode = false;
    private bool showModal = false;
    private byte[]? uploadedImage;
	private int staffToDeleteId;
    private async Task HandleProfileImageUpload(InputFileChangeEventArgs e)
    {
        var file = e.File;

        if (file != null)
        {
            using var stream = new MemoryStream();
            await file.OpenReadStream(maxAllowedSize: 5 * 1024 * 1024).CopyToAsync(stream); // limit 5 MB
            uploadedImage = stream.ToArray();
        }
    }
    private async Task onDeleteConfirm(bool confirmed)
    {
        if(confirmed)
        {
			showModal = false;
			await DeleteStaff(staffToDeleteId);
		}
		else
		{
			showModal = false;

			
		}
		
    }
    protected override async Task OnInitializedAsync()
    {
        positions = (await positionRepository.GetAllAsync("GetAllPositions")).ToList();
        await LoadStaff();
    }

    private async Task LoadStaff()
    {
        staffList = (await staffRepository.GetAllAsync("GetAllStaff")).ToList();
    }

    private async Task HandleSubmit()
    {
        if (newStaff.PositionId == 0)
        {
            // Optionally show a validation message or feedback
            return;
        }

      
            await staffRepository.AddAsync("AddStaff", new
            {
                newStaff.Name,
                newStaff.LastName,
                newStaff.FatherName,
                newStaff.Age,
                PositionId = newStaff.PositionId,

			ProfileImage = uploadedImage
            });
       

        newStaff = new();
		uploadedImage = null;
        isEditMode = false;
        await LoadStaff();
    }
    private void GoToDetails(int staffid)
    {
        NavigationManager.NavigateTo("/staff-details/" + staffid);

    }

   

    private async Task DeleteStaff(int id)
    {
        Console.WriteLine($"Deleting staff with id {id}");
        await staffRepository.DeleteAsync("DeleteStaff", new { StaffId = id });
		messages.SetMessages("Staff deleted successfully.", "Success");
        await LoadStaff();
    }

    private  Task ConfirmDelete(int id)
    {
		showModal = true;
		staffToDeleteId = id;
		return Task.CompletedTask;
       
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newStaff = new();
    }

  
}
