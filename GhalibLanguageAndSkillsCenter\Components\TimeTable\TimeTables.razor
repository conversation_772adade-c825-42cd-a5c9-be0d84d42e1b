﻿@page "/timetable"
@inject IGenericRepository<TimeTableModel> TimeTableRepo
@inject IGenericRepository<string> TimeTableCrudRepo
@inject IJSRuntime JS
@rendermode InteractiveServer
@attribute [Authorize]
<PageTitle>Time Table Management</PageTitle>

<div class="container-fluid mt-4">
    <div class="card shadow-lg border-0 rounded-4 p-4 bg-white">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3 class="mb-0">📅 Time Table Management</h3>
            <div class="d-flex gap-2">
                <button class="btn btn-success" @onclick="ToggleAddMode" disabled="@(SelectedProgramId == 0 || SelectedSectionId == 0)">
                    @if (isAddingNew)
                    {
                        <i class="fas fa-times"></i>
                        <span>Cancel Add</span>
                            }
                    else
                    {
                        <i class="fas fa-plus"></i>
                        <span> Add New Class</span>
                            }
                </button>
                <button class="btn btn-primary" @onclick="LoadTimeTables" disabled="@(SelectedProgramId == 0 || SelectedSectionId == 0)">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>

        <!-- Program and Section Selection -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <label class="form-label fw-bold">📚 Select Program</label>
                <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectProgram SelectedProgramId="@SelectedProgramId"
                                                                                            SelectedProgramIdChanged="@OnProgramChanged" />
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label fw-bold">🏫 Select Section</label>
                <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectSection SelectedSectionId="@SelectedSectionId"
                                                                                            SelectedSectionIdChanged="@OnSectionChanged" />
            </div>
        </div>

        <!-- Add New Class Form -->
        @if (isAddingNew && SelectedProgramId > 0 && SelectedSectionId > 0)
        {
            <div class="card border-success mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-plus-circle"></i> Add New Class</h5>
                </div>
                <div class="card-body">
                    <EditForm Model="@newTimeTable" OnValidSubmit="AddNewClass">
                        <DataAnnotationsValidator />
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">📅 Day of Week</label>
                                <InputSelect class="form-select" @bind-Value="newTimeTable.DayOfWeek">
                                    <option value="">Select a day</option>
                                    @foreach (var day in DaysOfWeek)
                                    {
                                        <option value="@day">@day</option>
                                    }
                                </InputSelect>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">🕐 Start Time</label>
                                <InputText class="form-control" @bind-Value="newTimeTable.StartTime" type="time" />
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">🕕 End Time</label>
                                <InputText class="form-control" @bind-Value="newTimeTable.EndTime" type="time" />
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">📖 Subject</label>
                                <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectSubject SelectedSubjectId="@newTimeTable.SubjectId"
                                                                                                            SelectedSubjectIdChanged="@((int id) => newTimeTable.SubjectId = id)"
                                                                                                            ProgramId="@SelectedProgramId" />
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button class="btn btn-success w-100" type="submit">
                                    <i class="fas fa-save"></i> Add Class
                                </button>
                            </div>
                        </div>
                        <ValidationSummary class="text-danger mt-2" />
                    </EditForm>
                </div>
            </div>
        }

        @if (!string.IsNullOrEmpty(messages.Message))
        {

            <SnackbarMessages Message="@messages.Message" Type="@messages.Type" />
        }

        <!-- Time Table Display -->
        @if (SelectedProgramId > 0 && SelectedSectionId > 0)
        {
            <div class="table-responsive">
                <table class="table table-bordered text-center align-middle timetable-table">
                    <thead class="table-dark">
                        <tr>
                            @foreach (var day in DaysOfWeek)
                            {
                                <th class="day-header">
                                    <i class="fas fa-calendar-day"></i> @day
                                </th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            @foreach (var day in DaysOfWeek)
                            {
                                var entries = result
                                .Where(e => e.DayOfWeek.Equals(day, StringComparison.OrdinalIgnoreCase))
                                .OrderBy(e => e.StartTime)
                                .ToList();

                                <td class="day-cell">
                                    @if (entries.Any())
                                    {
                                        @foreach (var entry in entries)
                                        {
                                            <div class="class-entry @(editingEntry?.TimeTableId == entry.TimeTableId ? "editing" : "")">
                                                @if (editingEntry?.TimeTableId == entry.TimeTableId)
                                                {
                                                    <!-- Edit Mode -->
                                                    <EditForm Model="@editingEntry" OnValidSubmit="SaveEdit">
                                                        <div class="edit-form">
                                                            <div class="mb-2">
                                                                <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectSubject SelectedSubjectId="@editingEntry.SubjectId"
                                                                                                                                            SelectedSubjectIdChanged="@((int id) => editingEntry.SubjectId = id)"
                                                                                                                                            ProgramId="@SelectedProgramId" />
                                                            </div>
                                                            <div class="row g-1 mb-2">
                                                                <div class="col-6">
                                                                    <InputText class="form-control form-control-sm" @bind-Value="editingEntry.StartTime" type="time" />
                                                                </div>
                                                                <div class="col-6">
                                                                    <InputText class="form-control form-control-sm" @bind-Value="editingEntry.EndTime" type="time" />
                                                                </div>
                                                            </div>
                                                            <div class="d-flex gap-1">
                                                                <button type="submit" class="btn btn-success btn-sm flex-fill">
                                                                    <i class="fas fa-save"></i><span>Update</span>
                                                                </button>
                                                                <button type="button" class="btn btn-secondary btn-sm flex-fill" @onclick="CancelEdit">
                                                                    <i class="fas fa-times"></i><span>Cancel</span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </EditForm>
                                                }
                                                else
                                                {
                                                    <!-- Display Mode -->
                                                    <div class="class-info">
                                                        <div class="subject-name">@entry.SubjectName</div>
                                                        <div class="time-info">@entry.StartTime - @entry.EndTime</div>
                                                        <div class="action-buttons">
                                                            <button class="btn btn-outline-primary btn-sm me-1" @onclick="() => StartEdit(entry)">
                                                                <i class="fas fa-edit"></i><span>Edit</span>
                                                            </button>
                                                            <button class="btn btn-outline-danger btn-sm" @onclick="() => DeleteClass(entry.TimeTableId)">
                                                                <i class="fas fa-trash"></i><span>Delete</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="no-classes">
                                            <i class="fas fa-calendar-times text-muted"></i>
                                            <div class="text-muted">No classes</div>
                                        </div>
                                    }
                                </td>
                            }
                        </tr>
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle"></i> Please select a Program and a Section to view the timetable.
            </div>
        }
    </div>
</div>

@code {
    private int SelectedProgramId;
    private int SelectedSectionId;
    private List<TimeTableModel> result = new();
    private TimeTableModel newTimeTable = new();
    private TimeTableModel? editingEntry;
    private bool isAddingNew = false;
	private Messages messages = new Messages();

    private readonly List<string> DaysOfWeek = new()
    {
        "Saturday", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday"
    };

    private async Task OnProgramChanged(int id)
    {
        SelectedProgramId = id;
        newTimeTable.ProgramId = id;
        newTimeTable.SubjectId = 0; // Reset subject when program changes
        await LoadTimeTables();
    }

    private async Task OnSectionChanged(int id)
    {
        SelectedSectionId = id;
        newTimeTable.SectionId = id;
        await LoadTimeTables();
    }

    private async Task LoadTimeTables()
    {
        try
        {
            if (SelectedProgramId > 0 && SelectedSectionId > 0)
            {
                result = (await TimeTableRepo.GetAllAsyncById("GetTimeTableByProgramAndSection",
                    new { ProgramId = SelectedProgramId, SectionId = SelectedSectionId })).ToList();
            }
            else
            {
                result.Clear();
            }
        }
        catch (Exception ex)
        {
           
			messages.SetMessages("Error loading timetables: " + ex.Message, "Error");
        }
    }

    private void ToggleAddMode()
    {
        isAddingNew = !isAddingNew;
        if (isAddingNew)
        {
            newTimeTable = new TimeTableModel
                {
                    ProgramId = SelectedProgramId,
                    SectionId = SelectedSectionId
                };
            editingEntry = null; // Cancel any ongoing edit
        }
    }

    private async Task AddNewClass()
    {
        try
        {
            if (string.IsNullOrEmpty(newTimeTable.DayOfWeek) ||
                string.IsNullOrEmpty(newTimeTable.StartTime) ||
                string.IsNullOrEmpty(newTimeTable.EndTime) ||
                newTimeTable.SubjectId == 0)
            {
           
				messages.SetMessages("Please fill in all required fields.", "Error"); 
                return;
            }

            var parameters = new
            {
                newTimeTable.DayOfWeek,
                newTimeTable.StartTime,
                newTimeTable.EndTime,
                newTimeTable.SubjectId,
                ProgramId = SelectedProgramId,
                SectionId = SelectedSectionId
            };

            await TimeTableCrudRepo.AddAsync("AddTimeTable", parameters);

      
			messages.SetMessages("Class added successfully!", "Success");
            isAddingNew = false;
            newTimeTable = new();
            await LoadTimeTables();
        }
        catch (Exception ex)
        {
           
			messages.SetMessages("Error adding class: " + ex.Message, "Error");
        }
    }

    private void StartEdit(TimeTableModel entry)
    {
        editingEntry = new TimeTableModel
            {
                TimeTableId = entry.TimeTableId,
                DayOfWeek = entry.DayOfWeek,
                StartTime = entry.StartTime,
                EndTime = entry.EndTime,
                SubjectId = entry.SubjectId,
                SubjectName = entry.SubjectName,
                ProgramId = entry.ProgramId,
                SectionId = entry.SectionId
            };
        isAddingNew = false; // Cancel add mode if active
    }

    private void CancelEdit()
    {
        editingEntry = null;
    }

    private async Task SaveEdit()
    {
        try
        {
            if (editingEntry == null) return;

            var parameters = new
            {
                TimeTableId = editingEntry.TimeTableId,
                editingEntry.DayOfWeek,
                editingEntry.StartTime,
                editingEntry.EndTime,
                editingEntry.SubjectId,
                ProgramId = SelectedProgramId,
                SectionId = SelectedSectionId
            };

            await TimeTableCrudRepo.UpdateAsync("UpdateTimeTable", parameters);

         
			messages.SetMessages("Class updated successfully!", "Success");
            editingEntry = null;
            await LoadTimeTables();
        }
        catch (Exception ex)
        {
         
			messages.SetMessages($"Error updating class: {ex.Message}", "Error");
        }
    }

    private async Task DeleteClass(int timeTableId)
    {
        try
        {
            if (await JS.InvokeAsync<bool>("confirm", "Are you sure you want to delete this class?"))
            {
                await TimeTableCrudRepo.DeleteAsync("DeleteTimeTable", new { TimeTableId = timeTableId });

                messages.Message = "Class deleted successfully!";
				messages.Type = "Success";
                await LoadTimeTables();
            }
        }
        catch (Exception ex)
        {
           
            messages.SetMessages("Error deleting class: " + ex.Message, "Error");
        }
    }
}

<style>
    <style >
    .timetable-table {
        border-collapse: collapse;
        width: 100%;
        border-radius: 12px;
        overflow: hidden;
    }

    .day-header {
        background: linear-gradient(145deg, #007bff, #0056b3);
        color: #fff;
        font-weight: 600;
        padding: 16px;
        font-size: 1rem;
        border: none;
    }

    .day-cell {
        background-color: #f4f6f9;
        padding: 12px;
        border: 1px solid #e3e6ea;
        vertical-align: top;
        min-height: 200px;
    }

    .class-entry {
        background: #ffffff;
        border: 1px solid #d1d5db;
        border-radius: 12px;
        padding: 12px;
        margin-bottom: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        position: relative;
    }

        .class-entry:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .class-entry.editing {
            border-left: 5px solid #28a745;
            background-color: #f2fef5;
        }

    .class-info {
        text-align: center;
    }

    .subject-name {
        font-weight: 600;
        color: #2f3e4e;
        margin-bottom: 5px;
        font-size: 0.95rem;
    }

    .time-info {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 6px;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 6px;
        transition: opacity 0.3s ease;
    }

    .class-entry:hover .action-buttons {
        opacity: 1;
    }

    .no-classes {
        text-align: center;
        padding: 50px 10px;
        color: #adb5bd;
        font-size: 0.95rem;
    }

    .edit-form {
        padding: 10px;
        background-color: #fdfdfd;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    .btn-outline-primary.btn-sm, .btn-outline-danger.btn-sm {
        font-size: 0.75rem;
        padding: 4px 8px;
        border-radius: 6px;
    }

        .btn-outline-primary.btn-sm i, .btn-outline-danger.btn-sm i {
            margin-right: 4px;
        }

    .card.shadow-lg {
        border-radius: 16px;
        border: none;
    }

    .form-label.fw-bold {
        font-size: 0.95rem;
        color: #2d3748;
    }

    button.btn-success[type="submit"] {
        border-radius: 8px;
        padding: 8px 12px;
        font-weight: 600;
    }
</style>


</style>