﻿@* Shared/Components/SnackbarMessages.razor *@
@inject MessageService MessageService
@using Blazorise.Snackbar

<Snackbar @ref="snackbarSuccess" Color="SnackbarColor.Success" AutoHideTimeout="5000" Class="custom-snackbar success-snackbar">
    <SnackbarBody>
        <i class="bi bi-check-circle-fill me-2"></i>
        <span>@_message</span>
        <SnackbarAction Clicked="() => snackbarSuccess.Hide()" Class="ms-3 btn-close-icon">
            <i class="bi bi-x-lg"></i>
        </SnackbarAction>
    </SnackbarBody>
</Snackbar>

<Snackbar @ref="snackbarError" Color="SnackbarColor.Danger" AutoHideTimeout="7000" Class="custom-snackbar error-snackbar">
    <SnackbarBody>
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span>@_message</span>
        <SnackbarAction Clicked="() => snackbarError.Hide()" Class="ms-3 btn-close-icon">
            <i class="bi bi-x-lg"></i>
        </SnackbarAction>
    </SnackbarBody>
</Snackbar>

@code {
    private Snackbar snackbarSuccess;
    private Snackbar snackbarError;

    private string _message;
    private string _type;

    protected override void OnInitialized()
    {
        MessageService.OnMessage += ShowMessage;
    }

    private async void ShowMessage(string message, string type)
    {
        _message = message;
        _type = type;

        if (_type == "Success")
        {
            await snackbarSuccess.Show();
        }
        else if (_type == "Error")
        {
            await snackbarError.Show();
        }

        StateHasChanged();
    }

    public void Dispose()
    {
        MessageService.OnMessage -= ShowMessage;
    }
}
