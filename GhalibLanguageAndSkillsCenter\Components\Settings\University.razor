﻿@page "/university"
@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Components.Forms
@inject IGenericRepository<UniversityModel> UniversityRepo
@inject IJSRuntime JS
@rendermode InteractiveServer
@attribute [Authorize]

<h3>@(isEditMode ? "Edit University" : "Add University")</h3>
@if (!string.IsNullOrEmpty(messages.Message))
{
    <SnackbarMessages Message="@messages.Message" Type="@messages.Type" />
}

<EditForm Model="@newUniversity" OnValidSubmit="AddOrUpdateUniversity">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group mb-3">
        <label for="englishName">English Name</label>
        <InputText id="englishName" class="form-control" @bind-Value="newUniversity.EnglishName" />
        <ValidationMessage For="@(() => newUniversity.EnglishName)" />
    </div>

    <div class="form-group mb-3">
        <label for="dariName">Dari Name</label>
        <InputText id="dariName" class="form-control" @bind-Value="newUniversity.DariName" />
        <ValidationMessage For="@(() => newUniversity.DariName)" />
    </div>

    <div class="form-group mb-3">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newUniversity.PashtoName" />
        <ValidationMessage For="@(() => newUniversity.PashtoName)" />
    </div>

    <button type="submit" class="btn btn-primary">
        @(isEditMode ? "Update University" : "Add University")
    </button>
    @if (isEditMode)
    {
        <button type="button" class="btn btn-secondary ms-2" @onclick="CancelEdit">Cancel</button>
    }
    </EditForm>

    <hr />

    <h3>University List</h3>

    @if (universities?.Any() == true)
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English Name</th>
                <th>Dari Name</th>
                <th>Pashto Name</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var uni in universities)
            {
                <tr>
                    <td>@uni.UniversityId</td>
                    <td>@uni.EnglishName</td>
                    <td>@uni.DariName</td>
                    <td>@uni.PashtoName</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditUniversity(uni)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(uni.UniversityId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No universities available.</p>
}
<DeleteModal IsVisible="@showModal" IsVisibleChanged="@((value)=>showModal = value)" OnDeleteConfirmed="@onDeleteConfirm" />

@code {

    private UniversityModel newUniversity = new();
    private bool showModal = false;

    private Messages messages = new();
	private int universityIdToDelete;
	private List<UniversityModel> universities = new();
    private bool isEditMode;

    protected override async Task OnInitializedAsync()
    {
        await LoadUniversities();
    }

	private async Task onDeleteConfirm(bool confirmed)
	{
		if (confirmed)
		{
			await DeleteUniversity(universityIdToDelete);
			showModal = false;
		}
        else
        {
			showModal = false;
		}
	}
	private async Task LoadUniversities()
    {
        universities = (await UniversityRepo.GetAllAsync("GetAllUniversities")).ToList();
    }

    private async Task AddOrUpdateUniversity()
    {
        try
        {
            if (isEditMode)
            {
                await UniversityRepo.UpdateAsync("UpdateUniversity", newUniversity);
				messages.SetMessages("University updated successfully.", "Success");
			}
            else
            {
                await UniversityRepo.AddAsync("CreateUniversity", new
                {
                    EnglishName = newUniversity.EnglishName,
                    DariName = newUniversity.DariName,
                    PashtoName = newUniversity.PashtoName
                });
				messages.SetMessages("University added successfully.", "Success");
			}

            CancelEdit();
            await LoadUniversities();
        }
        catch (Exception ex)
        {
			messages.SetMessages("An error occurred while saving the university: " + ex.Message, "Error");
        }
    }

    private void EditUniversity(UniversityModel uni)
    {
        isEditMode = true;
        newUniversity = new UniversityModel
        {
            UniversityId = uni.UniversityId,
            EnglishName = uni.EnglishName,
            DariName = uni.DariName,
            PashtoName = uni.PashtoName
        };
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newUniversity = new UniversityModel();
    }

    private void  ConfirmDelete(int universityId)
    {

		showModal = true;
		universityIdToDelete = universityId;
		

	}

    private async Task DeleteUniversity(int universityId)
    {
        try
        {
            await UniversityRepo.DeleteAsync("DeleteUniversity", new { UniversityId = universityId });
            await LoadUniversities();
			messages.SetMessages("University deleted successfully.", "Success");
		
		}
        catch (Exception ex)
        {
			messages.SetMessages("An error occurred while deleting the university: " + ex.Message, "Error"); 

	}
    }

 
   
}
