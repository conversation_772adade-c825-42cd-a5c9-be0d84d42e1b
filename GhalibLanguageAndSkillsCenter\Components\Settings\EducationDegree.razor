﻿@page "/education-degree"
@inject IGenericRepository<StaffDegreeModel> EducationDegreeRepo
@inject IJSRuntime JS
@using System.ComponentModel.DataAnnotations
@rendermode InteractiveServer
@attribute [Authorize]

<h3>@(isEditMode ? "Edit Education Degree" : "Add Education Degree")</h3>
@if (!string.IsNullOrEmpty(messages.Message))
{
    <SnackbarMessages Message="@messages.Message" Type="@messages.Type" />
}

<EditForm Model="@newDegree" OnValidSubmit="AddOrUpdateDegree">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group mb-3">
        <label for="englishName">English Name</label>
        <InputText id="englishName" class="form-control" @bind-Value="newDegree.EnglishName" />
        <ValidationMessage For="@(() => newDegree.EnglishName)" />
    </div>

    <div class="form-group mb-3">
        <label for="dariName">Dari Name</label>
        <InputText id="dariName" class="form-control" @bind-Value="newDegree.DariName" />
        <ValidationMessage For="@(() => newDegree.DariName)" />
    </div>

    <div class="form-group mb-3">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newDegree.PashtoName" />
        <ValidationMessage For="@(() => newDegree.PashtoName)" />
    </div>

    <button type="submit" class="btn btn-primary">
        @(isEditMode ? "Update Degree" : "Add Degree")
    </button>

    @if (isEditMode)
    {
        <button type="button" class="btn btn-secondary ms-2" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

<hr />

<h4>All Education Degrees</h4>

@if (degrees != null && degrees.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>ID</th>
                <th>English</th>
                <th>Dari</th>
                <th>Pashto</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var deg in degrees)
            {
                <tr>
                    <td>@deg.EducationDegreeId</td>
                    <td>@deg.EnglishName</td>
                    <td>@deg.DariName</td>
                    <td>@deg.PashtoName</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditDegree(deg)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ShowDeleteModal(deg.EducationDegreeId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No records found.</p>
}
<DeleteModal IsVisible="@showModal" IsVisibleChanged="@((value)=>showModal = value)" OnDeleteConfirmed="@onDeleteConfirm" />

@code {
    private StaffDegreeModel newDegree = new();
    private List<StaffDegreeModel> degrees = new();
    private bool isEditMode = false;
    private bool showModal = false;
    private int EducationDegrreToDelete;
	private Messages messages = new();
    protected override async Task OnInitializedAsync()
    {
        await LoadDegrees();
    }

	private async Task onDeleteConfirm(bool confirmed)
	{
        if(confirmed)
        {
		await ConfirmDelete(EducationDegrreToDelete);
			showModal = false;

        }
        else{
			showModal = false;
        }
	}
    private async Task LoadDegrees()
    {
        degrees = (await EducationDegreeRepo.GetAllAsync("GetAllEducationDegrees")).ToList();
    }

    private async Task AddOrUpdateDegree()
    {
        if (isEditMode)
        {
            await EducationDegreeRepo.UpdateAsync("UpdateEducationDegree", newDegree);
			messages.SetMessages("Degree updated successfully.", "Success");
        }
        else
        {
            await EducationDegreeRepo.AddAsync("CreateEducationDegree", new
            {
                EnglishName = newDegree.EnglishName,
                DariName    = newDegree.DariName,
                PashtoName  = newDegree.PashtoName
            });
			messages.SetMessages("Degree added successfully.", "Success");
        }

        newDegree = new();
        isEditMode = false;
        await LoadDegrees();
    }

    private void EditDegree(StaffDegreeModel degree)
    {
        isEditMode = true;
        newDegree = new StaffDegreeModel
        {
            EducationDegreeId = degree.EducationDegreeId,
            EnglishName       = degree.EnglishName,
            DariName          = degree.DariName,
            PashtoName        = degree.PashtoName
        };
    }

    private void CancelEdit()
    {
        newDegree = new();
        isEditMode = false;
    }
	private void ShowDeleteModal(int id)
	{
		EducationDegrreToDelete = id;
		showModal = true;
	}

    private async Task ConfirmDelete(int id)
    {
      
            await EducationDegreeRepo.DeleteAsync("DeleteEducationDegree", new { EducationDegreeId = id });
		messages.SetMessages("Degree deleted successfully.", "Success");
            await LoadDegrees();
            
        
    }



  
}
