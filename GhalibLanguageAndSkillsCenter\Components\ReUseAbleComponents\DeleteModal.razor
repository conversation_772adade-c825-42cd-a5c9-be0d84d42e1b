﻿@using Blazorise
@rendermode InteractiveServer
<Modal @bind-Visible="IsVisible" BackdropDismiss="false" KeyboardDismiss="false">
    <ModalContent Centered>
        <ModalHeader>Confirm Deletion</ModalHeader>
        <ModalBody>
            Are you sure you want to delete this item?
        </ModalBody>
        <ModalFooter>
            <Button Color="Color.Danger" Clicked="ReadyToDelete">Yes</Button>
            <Button Color="Color.Secondary" Clicked="CancelDelete">No</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }

    [Parameter] public EventCallback<bool> OnDeleteConfirmed { get; set; }

    private async Task ReadyToDelete()
    {
        await OnDeleteConfirmed.InvokeAsync(true);
        await IsVisibleChanged.InvokeAsync(false); // Close modal
    }

    private async Task CancelDelete()
    {
        await OnDeleteConfirmed.InvokeAsync(false);
        await IsVisibleChanged.InvokeAsync(false); // Close modal
    }

}
