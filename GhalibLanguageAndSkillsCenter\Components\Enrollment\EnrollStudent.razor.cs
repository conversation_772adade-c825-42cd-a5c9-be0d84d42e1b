﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Authorization;

using GhalibLanguageAndSkillsCenter.Services;
using GhalibLanguageAndSkillsCenter.Models.Enrollment;
using GhalibLanguageAndSkillsCenter.Utilities;
using Microsoft.AspNetCore.Components.Forms;
using GhalibLanguageAndSkillsCenter.Models;

namespace GhalibLanguageAndSkillsCenter.Components.Enrollment;

[Authorize]
public partial class EnrollStudent : ComponentBase
{
    [Inject] private IGenericRepository<object> repository { get; set; }
    [Inject] private IGenericRepository<GetIdFromDatabase> getidnumberRepo { get; set; }
    [Inject] private IGenericRepository<ProgramModel> programRepository { get; set; }

    private EnrollStudentModel enrollmentModel = new();
    private List<ProgramModel> programs = new();
    
   
    private string enrollDate;
    private byte[] imageBytes;
    private GetIdFromDatabase generator = new();
    private Messages messages = new();
    protected override async Task OnInitializedAsync()
    {
        programs = (await programRepository.GetAllAsync("GetAllPrograms")).ToList();
       
    }

    private void OnSectionChanged(int id)
    {
        enrollmentModel.SectionId = id;
    }
    private void OnProgramChange(int id)
    {
        enrollmentModel.ProgramId = id;
    }
    private void OnShiftChange(int id)
    {
        enrollmentModel.ShiftId = id;
    }
    private async Task HandleImageUpload(InputFileChangeEventArgs e)
    {
        try
        {
            var file = e.File;
            if (file != null)
            {
                using var ms = new MemoryStream();
                await file.OpenReadStream(5 * 1024 * 1024).CopyToAsync(ms); // 5MB limit
                enrollmentModel.ProfileImage = ms.ToArray(); // Store byte[] directly
            }
        }
        catch (Exception ex)
        {
          
            messages.SetMessages("Error uploading image.", "Error");
        }
    }

    private async Task HandleEnroll()
    {
        enrollmentModel.EnrollmentDate = PersianDateConverter.ToDateTime(enrollDate);
        if (enrollmentModel.ProgramId == 0 || enrollmentModel.SectionId == 0)
        {
           
            messages.SetMessages("Please select a Program and Section.", "Error");

            return;
        }

        try
        {
            enrollmentModel.StudentIdNumber = await GenerateStudentIdAsync();
            await repository.AddAsync("RegisterStudentAndEnroll", new
            {
                enrollmentModel.Name,
                enrollmentModel.LastName,
                enrollmentModel.FatherName,
                enrollmentModel.Contact,
                enrollmentModel.ProgramId,
                enrollmentModel.SectionId,
                enrollmentModel.ShiftId,
                enrollmentModel.EnrollmentDate,
                enrollmentModel.StudentIdNumber,
                enrollmentModel.ProfileImage
            });

           
            messages.SetMessages("Student successfully registered and enrolled!", "Success");
            enrollmentModel = new(); // Clear form
        }
        catch (Exception ex)
        {
           
          
            messages.SetMessages("An error occurred while enrolling the student.", "Error");
        }
    }
    private async Task<string> GenerateStudentIdAsync()
    {
        // 1) Define the fixed prefix
        const string prefix = "GLSC";

        // 2) Call the stored procedure via getidnumberRepo
        //    We expect exactly one row (or none) with a column StudentIdNumber.
        GetIdFromDatabase? result =
            await getidnumberRepo.GetByIdAsync(
                "GetLatestStudentIdByPrefix",
                new { Prefix = prefix }
            );

        // 3) Extract the latest ID string (if any)
        string? latestId = result?.StudentIdNumber;

        // 4) Compute the next numeric part
        int nextNumber = 1;
        if (!string.IsNullOrWhiteSpace(latestId)
            && latestId.Length > prefix.Length)
        {
            // e.g. latestId == "GLSC00042" → numberPart = "00042"
            string numberPart = latestId.Substring(prefix.Length);
            if (int.TryParse(numberPart, out int parsed))
            {
                nextNumber = parsed + 1;
            }
        }

        // 5) Return the new ID: "GLSC" + zero‐padded 5 digits
        //    e.g. 1 → "GLSC00001", 42 → "GLSC00042", etc.
        return $"{prefix}{nextNumber:D5}";
    }


    class GetIdFromDatabase
    {
        public string StudentIdNumber { get; set; }

    }
}
