﻿@page "/StaffSalary"
@inject IGenericRepository<PositionModel> PositionRepo
@inject IGenericRepository<StaffModel> StaffRepo
@inject IGenericRepository<StaffSalaryModel> StaffSalaryRepo
@inject IGenericRepository<StaffSalaryPaymentModel> PaymentRepo
@rendermode InteractiveServer
@attribute [Authorize]
@using Dapper

<h3>Staff Salary Management</h3>



<div class="row mb-3">
    <div class="col-md-6">
        <label>Select Position</label>
        <InputSelect @bind-Value="SelectedPosition" class="form-select" @onchange="OnPositionChanged">
            <option value="">Select Position</option>
            @foreach (var position in Positions)
            {
                <option value="@position.PositionId">@position.EnglishName</option>
            }
        </InputSelect>
    </div>

    <div class="col-md-6">
        <label>Select Staff</label>
        <InputSelect @bind-Value="SelectedStaffId" class="form-select" disabled="@(!FilteredStaffs.Any())">
            <option value="">Select Staff</option>
            @foreach (var staff in FilteredStaffs)
            {
                <option value="@staff.StaffId">@staff.Name</option>
            }
        </InputSelect>
    </div>
</div>

@if (CurrentSalary != null)
{
    <div class="card p-3 shadow-sm">
        <div class="d-flex gap-3 mb-4">
            <div class="card text-white bg-success flex-fill" style="max-width: 200px;">
                <div class="card-body p-3">
                    <h6 class="card-title mb-1">Total Paid</h6>
                    <p class="card-text fs-4 fw-bold m-0">@TotalPaid</p>
                </div>
            </div>

            <div class="card text-white bg-danger flex-fill" style="max-width: 200px;">
                <div class="card-body p-3">
                    <h6 class="card-title mb-1">Remaining Due</h6>
                    <p class="card-text fs-4 fw-bold m-0">@RemainingDue</p>
                </div>
            </div>
        </div>

        <div class="row g-3">
            <div class="col-md-4">
                <label>Year-Month (e.g., 2025-07)</label>
                <InputText @bind-Value="CurrentSalary.YearMonth" class="form-control" />
            </div>

            <div class="col-md-4">
                <label>Amount Due</label>
                <InputNumber @bind-Value="CurrentSalary.AmountDue" class="form-control" />
            </div>

            <div class="col-md-4">
                <label>Paid Date</label>
                <InputDate @bind-Value="PaymentModel.PaidDate" class="form-control" />
            </div>

            <div class="col-md-4">
                <label>Amount Paid</label>
                <InputNumber @bind-Value="PaymentModel.AmountPaid" class="form-control" />
            </div>

            <div class="col-md-8">
                <label>Remarks</label>
                <InputText @bind-Value="PaymentModel.Remarks" class="form-control" />
            </div>
        </div>

        <div class="mt-4 d-flex justify-content-end">
            <button class="btn btn-primary" @onclick="SaveAll" disabled="@(!IsFormValid)">
                Save
            </button>
        </div>
    </div>

    <h5 class="mt-5">Recent Payments (Top 5)</h5>
    @if (CurrentStaffPayments.Any())
    {
        <table class="table table-hover mt-2">
            <thead class="table-light">
                <tr>
                    <th>Paid Date</th>
                    <th>Amount Paid</th>
                    <th>Remarks</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var payment in CurrentStaffPayments.Take(5))
                {
                    <tr>
                        <td>@payment.PaidDate.ToString("yyyy-MM-dd")</td>
                        <td>@payment.AmountPaid</td>
                        <td>@payment.Remarks</td>
                    </tr>
                }
            </tbody>
        </table>
    }
    else
    {
        <p class="text-muted">No payments found.</p>
    }
}
@if (!string.IsNullOrEmpty(messages.Message))
{

    <SnackbarMessages Message="@messages.Message" Type="@messages.Type" />
}
@code {
    List<PositionModel> Positions { get; set; } = new();
    List<StaffModel> AllStaffs { get; set; } = new();
    List<StaffSalaryModel> AllStaffSalaries { get; set; } = new();
    List<StaffSalaryPaymentModel> AllPayments { get; set; } = new();
	private Messages messages = new Messages();
    List<StaffModel> FilteredStaffs =>
        AllStaffs.Where(s => s.PositionId == SelectedPosition).ToList();

    private int selectedPosition;
    int SelectedPosition
    {
        get => selectedPosition;
        set
        {
            if (selectedPosition != value)
            {
                selectedPosition = value;
                SelectedStaffId = 0;
                CurrentSalary = null;
                PaymentModel = new StaffSalaryPaymentModel();
                CurrentStaffPayments.Clear();
            }
        }
    }

    private int selectedStaffId;
    int SelectedStaffId
    {
        get => selectedStaffId;
        set
        {
            selectedStaffId = value;
            if (selectedStaffId > 0)
            {
                var currentYearMonth = DateTime.Now.ToString("yyyy-MM");

                CurrentSalary = AllStaffSalaries
                    .FirstOrDefault(s => s.StaffId == selectedStaffId && s.YearMonth == currentYearMonth)
                    ?? new StaffSalaryModel
                        {
                            StaffSalaryId = 0,
                            StaffId = selectedStaffId,
                            YearMonth = currentYearMonth,
                            AmountDue = 0
                        };

                LoadCurrentStaffPayments();

                PaymentModel = new StaffSalaryPaymentModel
                    {
                        PaymentId = 0,
                        SalaryId = CurrentSalary.StaffSalaryId,
                        PaidDate = DateTime.Today,
                        AmountPaid = 0,
                        Remarks = string.Empty
                    };
            }
            else
            {
                CurrentSalary = null;
                PaymentModel = new();
                CurrentStaffPayments.Clear();
            }
        }
    }

    StaffSalaryModel? CurrentSalary { get; set; }
    StaffSalaryPaymentModel PaymentModel { get; set; } = new();

    List<StaffSalaryPaymentModel> CurrentStaffPayments { get; set; } = new();

  

    bool IsFormValid =>
        CurrentSalary != null &&
        CurrentSalary.StaffId > 0 &&
        !string.IsNullOrWhiteSpace(CurrentSalary.YearMonth) &&
        CurrentSalary.AmountDue >= 0 &&
        PaymentModel.AmountPaid >= 0 &&
        PaymentModel.PaidDate != default;

    int TotalPaid => CurrentStaffPayments.Sum(p => p.AmountPaid);

    int RemainingDue => CurrentSalary == null ? 0 : Math.Max(0, CurrentSalary.AmountDue - TotalPaid);

    protected override async Task OnInitializedAsync()
    {
        Positions = (await PositionRepo.GetAllAsync("GetAllPositions")).ToList();
        AllStaffs = (await StaffRepo.GetAllAsync("GetAllStaff")).ToList();
        AllStaffSalaries = (await StaffSalaryRepo.GetAllAsync("GetAllStaffSalary")).ToList();
        AllPayments = (await PaymentRepo.GetAllAsync("GetAllStaffSalaryPayments")).ToList();
    }

    private void LoadCurrentStaffPayments()
    {
        if (CurrentSalary != null && CurrentSalary.StaffSalaryId > 0)
        {
            CurrentStaffPayments = AllPayments
                .Where(p => p.SalaryId == CurrentSalary.StaffSalaryId)
                .OrderByDescending(p => p.PaidDate)
                .ToList();
        }
        else
        {
            CurrentStaffPayments = new List<StaffSalaryPaymentModel>();
        }
    }

    private async Task OnPositionChanged(ChangeEventArgs e)
    {
        // This method intentionally left empty because SelectedPosition setter already clears necessary data
        await InvokeAsync(StateHasChanged);
    }

    private async Task SaveAll()
    {
        if (CurrentSalary == null)
            return;

        try
        {
            var salaryParams = new DynamicParameters();
            salaryParams.Add("@StaffSalaryId", CurrentSalary.StaffSalaryId);
            salaryParams.Add("@StaffId", CurrentSalary.StaffId);
            salaryParams.Add("@YearMonth", CurrentSalary.YearMonth);
            salaryParams.Add("@AmountDue", CurrentSalary.AmountDue);

            if (CurrentSalary.StaffSalaryId > 0)
            {
                await StaffSalaryRepo.UpdateAsync("UpsertStaffSalary", salaryParams);
            }
            else
            {
                await StaffSalaryRepo.AddAsync("UpsertStaffSalary", salaryParams);
            }

            AllStaffSalaries = (await StaffSalaryRepo.GetAllAsync("GetAllStaffSalary")).ToList();

            var updatedSalary = AllStaffSalaries
                .FirstOrDefault(s => s.StaffId == CurrentSalary.StaffId && s.YearMonth == CurrentSalary.YearMonth);

            if (updatedSalary != null)
            {
                CurrentSalary.StaffSalaryId = updatedSalary.StaffSalaryId;
            }

            if (PaymentModel.AmountPaid > 0)
            {
                PaymentModel.SalaryId = CurrentSalary.StaffSalaryId;

                var paymentParams = new DynamicParameters();
                paymentParams.Add("@PaymentId", PaymentModel.PaymentId);
                paymentParams.Add("@SalaryId", PaymentModel.SalaryId);
                paymentParams.Add("@PaidDate", PaymentModel.PaidDate);
                paymentParams.Add("@AmountPaid", PaymentModel.AmountPaid);
                paymentParams.Add("@Remarks", PaymentModel.Remarks);

                if (PaymentModel.PaymentId > 0)
                {
                    await PaymentRepo.UpdateAsync("UpsertStaffSalaryPayment", paymentParams);
                }
                else
                {
					if (RemainingDue - PaymentModel.AmountPaid < 0)
					{
						
						
						messages.SetMessages("Payment exceeds remaining due amount.", "Error");
						return;
					}
                    await PaymentRepo.AddAsync("UpsertStaffSalaryPayment", paymentParams);
                }
            }

            AllPayments = (await PaymentRepo.GetAllAsync("GetAllStaffSalaryPayments")).ToList();
            LoadCurrentStaffPayments();

            PaymentModel = new StaffSalaryPaymentModel
                {
                    PaymentId = 0,
                    SalaryId = CurrentSalary.StaffSalaryId,
                    PaidDate = DateTime.Today,
                    AmountPaid = 0,
                    Remarks = string.Empty
                };

          
			messages.SetMessages("Salary and payment saved successfully.", "Success");
          
        }
        catch (Exception ex)
        {
         
			messages.SetMessages("Error saving data: " + ex.Message, "Error"); 
        }
    }

    public class StaffSalaryModel
    {
        public int StaffSalaryId { get; set; }
        public int StaffId { get; set; }
        public string YearMonth { get; set; } = string.Empty;
        public int AmountDue { get; set; }
    }

    public class StaffSalaryPaymentModel
    {
        public int PaymentId { get; set; }
        public int SalaryId { get; set; }
        public DateTime PaidDate { get; set; } = DateTime.Today;
        public int AmountPaid { get; set; }
        public string Remarks { get; set; } = string.Empty;
    }
}
