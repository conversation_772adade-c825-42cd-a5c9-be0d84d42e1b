﻿@page "/addstaff"
@inject IGenericRepository<StaffDetailDto> staffDetailsRepo
@inject IGenericRepository<StaffEducationModel> staffEducationRepo
@inject IGenericRepository<StaffEducationFeild> fieldRepo
@inject IGenericRepository<StaffDegreeModel> degreeRepo
@inject IGenericRepository<UniversityModel> universityRepo
@inject IGenericRepository<PositionModel> positionRepo
@inject IGenericRepository<CountryDto> countryRepo
@inject IGenericRepository<ProvinceModel> provinceRepo
@rendermode InteractiveServer
@attribute [Authorize]




    <EditForm Model="this" OnValidSubmit="SaveAllAsync">
        <DataAnnotationsValidator />
        <div class="container mt-5 staff-details-container">
            <div class="card shadow-lg border-0 rounded-4 p-4 bg-white">
                <!-- Staff Edit Section -->
                <div class="text-center mb-4">
                    <label for="fileInput" class="pointer">
                        @if (staff.ProfileImage != null)
                        {
                            <img src="data:image/jpeg;base64,@Convert.ToBase64String(staff.ProfileImage)" class="profile-img-large rounded-circle shadow" />
                        }
                        else
                        {
                            <div class="no-image">Click to add image</div>
                        }
                    </label>
                    <InputFile id="fileInput" style="display:none" OnChange="OnProfileImageChange" />
                </div>
                <div class="row g-3 mb-4">
                    <div class="col-md-6">
                        <label>Name</label>
                        <InputText class="form-control" @bind-Value="staff.StaffName" />
                    </div>
                    <div class="col-md-6">
                        <label>Last Name</label>
                        <InputText class="form-control" @bind-Value="staff.LastName" />
                    </div>
                    <div class="col-md-6">
                        <label>Father Name</label>
                        <InputText class="form-control" @bind-Value="staff.FatherName" />
                    </div>
                    <div class="col-md-3">
                        <label>Age</label>
                        <InputNumber class="form-control" @bind-Value="staff.Age" />
                    </div>
                    <div class="col-md-3">
                        <label>Position</label>
                        <InputSelect class="form-select" @bind-Value="staff.PositionId">
                            <option value="0">-- Select Position --</option>
                            @foreach (var pos in positions)
                            {
                                <option value="@pos.PositionId">@pos.EnglishName</option>
                            }
                        </InputSelect>
                    </div>
                </div>

                <hr />

                
                <h4 class="mt-4 mb-3">➕ Add New Education</h4>
                <div class="border rounded-3 p-3 mb-3 bg-white edu-card">
                    <div class="row g-2">
                        <div class="col-md-4">
                            <label>📘 Field</label>
                            <InputSelect class="form-select" @bind-Value="newEducation.EducationFeild">
                                <option value="0">-- Select Field --</option>
                                @foreach (var field in fieldList)
                                {
                                    <option value="@field.EducationFeildId">@field.EnglishName</option>
                                }
                            </InputSelect>
                        </div>
                        <div class="col-md-4">
                            <label>🎓 Degree</label>
                            <InputSelect class="form-select" @bind-Value="newEducation.EducationDegreeId">
                                <option value="0">-- Select Degree --</option>
                                @foreach (var degree in degreeList)
                                {
                                    <option value="@degree.EducationDegreeId">@degree.EnglishName</option>
                                }
                            </InputSelect>
                        </div>
                        <div class="col-md-4">
                            <label>🏫 University</label>
                            <InputSelect class="form-select" @bind-Value="newEducation.UniversityId">
                                <option value="0">-- Select University --</option>
                                @foreach (var uni in universityList)
                                {
                                    <option value="@uni.UniversityId">@uni.EnglishName</option>
                                }
                            </InputSelect>
                        </div>
                        <div class="col-md-6">
                            <label>🌍 Country</label>
                            <select class="form-select" value="@newEducationCountryId"
                                    @onchange="@(e => UpdateNewEducationCountry(Convert.ToInt32(e.Value)))">
                                <option value="0">-- Select Country --</option>
                                @foreach (var country in countryList)
                                {
                                    <option value="@country.CountryId">@country.Country</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label>🏙️ Province</label>
                            <InputSelect class="form-select" @bind-Value="newEducation.ProvinceId">
                                <option value="0">-- Select Province --</option>
                                @foreach (var province in newEducationProvinces)
                                {
                                    <option value="@province.ProvinceId">@province.Province</option>
                                }
                            </InputSelect>
                        </div>
                        <div class="col-md-6">
                            <label>Start Date</label>
                            <InputDate class="form-control" @bind-Value="newEducation.EducationStartingDate" />
                        </div>
                        <div class="col-md-6">
                            <label>End Date</label>
                            <InputDate class="form-control" @bind-Value="newEducation.EducationEndingDate" />
                        </div>
                        <div class="col-md-6">
                            <label>Grading Scale</label>
                            <InputNumber class="form-control" @bind-Value="newEducation.GradingScale" />
                        </div>
                    </div>
                </div>

                <!-- Single Save Button -->
                <div class="text-end mt-3">
                    <button type="submit" class="btn btn-success btn-lg">💾 Save All Changes</button>
                </div>
            </div>
        </div>
    </EditForm>
@if (!string.IsNullOrEmpty(messages.Message))
{

    <SnackbarMessages Message="@messages.Message" Type="@messages.Type" />
}

@code {
   

    private StaffDetailDto staff = new();
    private List<StaffDetailDto> staffDetails = new();
    private StaffEducationModel newEducation = new() { EducationStartingDate = DateTime.Today, EducationEndingDate = DateTime.Today };
    private List<StaffEducationFeild> fieldList = new();
    private List<StaffDegreeModel> degreeList = new();
    private List<UniversityModel> universityList = new();
    private List<PositionModel> positions = new();
    private List<CountryDto> countryList = new();
    private List<ProvinceModel> provinceList = new();
	private Messages messages = new();

    // Education-specific country and province tracking
    private Dictionary<int, int> educationCountryMap = new();
    private Dictionary<int, List<ProvinceModel>> educationProvinceMap = new();
    private int newEducationCountryId = 0;
    private List<ProvinceModel> newEducationProvinces = new();

    protected override async Task OnInitializedAsync()
    {
        fieldList = (await fieldRepo.GetAllAsync("GetAllEducationFeilds")).ToList();
        degreeList = (await degreeRepo.GetAllAsync("GetAllEducationDegrees")).ToList();
        universityList = (await universityRepo.GetAllAsync("GetAllUniversities")).ToList();
        positions = (await positionRepo.GetAllAsync("GetAllPositions")).ToList();
        countryList = (await countryRepo.GetAllAsync("usp_GetAllCountries")).ToList();
        provinceList = (await provinceRepo.GetAllAsync("usp_GetAllProvinces")).ToList();
       
    }

    private int GetCountryForEducation(int educationId)
    {
        if (educationCountryMap.ContainsKey(educationId))
            return educationCountryMap[educationId];

        // Try to find the country based on the province
        var edu = staffDetails.FirstOrDefault(e => e.StaffEducationId == educationId);
        if (edu != null && edu.ProvinceId > 0)
        {
            var province = provinceList.FirstOrDefault(p => p.ProvinceId == edu.ProvinceId);
            if (province != null)
            {
                educationCountryMap[educationId] = province.CountryId;
                return province.CountryId;
            }
        }

        return 0;
    }

    private List<ProvinceModel> GetProvincesForEducation(int educationId)
    {
        if (educationProvinceMap.ContainsKey(educationId))
            return educationProvinceMap[educationId];

        int countryId = GetCountryForEducation(educationId);
        var provinces = provinceList.Where(p => p.CountryId == countryId).ToList();
        educationProvinceMap[educationId] = provinces;
        return provinces;
    }

    private void UpdateCountryForEducation(int educationId, int countryId)
    {
        educationCountryMap[educationId] = countryId;
        educationProvinceMap[educationId] = provinceList.Where(p => p.CountryId == countryId).ToList();

        // Reset province selection when country changes
        var edu = staffDetails.FirstOrDefault(e => e.StaffEducationId == educationId);
        if (edu != null)
        {
            edu.ProvinceId = 0;
        }

        StateHasChanged();
    }

    private void UpdateNewEducationCountry(int countryId)
    {
        newEducationCountryId = countryId;
        newEducationProvinces = provinceList.Where(p => p.CountryId == countryId).ToList();
        newEducation.ProvinceId = 0;
        StateHasChanged();
    }

    

    private async Task OnProfileImageChange(InputFileChangeEventArgs e)
    {
        try
        {
            var file = e.File;
            using var ms = new MemoryStream();
            await file.OpenReadStream(5 * 1024 * 1024).CopyToAsync(ms);
            staff.ProfileImage = ms.ToArray();
        }
        catch (Exception ex)
        {
            messages.Message = $"Error uploading image: {ex.Message}";
			messages.Type = "Error"; 
        }
    }

    private async Task SaveAllAsync()
    {
        try
        {
            // Update staff
            await staffDetailsRepo.AddAsync("AddStaff", new
            {
               
                Name = staff.StaffName,
                LastName = staff.LastName,
                FatherName = staff.FatherName,
                Age = staff.Age,
                PositionId = staff.PositionId,
                ProfileImage = staff.ProfileImage
            });

           
           

           
            if (newEducation.EducationFeild != 0 && newEducation.EducationDegreeId != 0 && newEducation.UniversityId != 0)
            {
                await staffEducationRepo.AddAsync("CreateStaffEducation", new
                {
                    EducationFeild = newEducation.EducationFeild,
                    EducationDegreeId = newEducation.EducationDegreeId,
                    UniversityId = newEducation.UniversityId,
                  
                    newEducation.EducationStartingDate,
                    newEducation.EducationEndingDate,
                    newEducation.GradingScale,
                    newEducation.ProvinceId
                });
            }

            messages.Message = "All changes saved successfully!";
			messages.Type = "Success";
            
        }
        catch (Exception ex)
        {
            messages.Message = $"Error saving changes: {ex.Message}";
			messages.Type = "Error";
        }
    }


    public class StaffDetailDto
    {
        public int StaffEducationId { get; set; }
        public int StaffId { get; set; }
        public string StaffName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FatherName { get; set; } = string.Empty;
        public int Age { get; set; }
        public int PositionId { get; set; }
        public byte[]? ProfileImage { get; set; }

        public int EducationFeildId { get; set; }
        public string EducationFeild { get; set; } = string.Empty;
        public int EducationDegreeId { get; set; }
        public string Degree { get; set; } = string.Empty;
        public int UniversityId { get; set; }
        public string University { get; set; } = string.Empty;
        public DateTime? EducationStartingDate { get; set; }
        public DateTime? EducationEndingDate { get; set; }
        public int? GradingScale { get; set; }
        public int ProvinceId { get; set; }
        public string Province { get; set; } = string.Empty;
    }

    public class CountryDto
    {
        public int CountryId { get; set; }
        public string Country { get; set; } = string.Empty;
    }

    public class ProvinceModel
    {
        public int ProvinceId { get; set; }
        public string Province { get; set; } = string.Empty;
        public int CountryId { get; set; }
    }
}

<style>
    .staff-details-container {
        max-width: 900px;
        margin: auto;
    }

    .profile-img-large {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border: 5px solid #dee2e6;
        background-color: #f8f9fa;
        cursor: pointer;
    }

    .no-image {
        width: 150px;
        height: 150px;
        line-height: 150px;
        text-align: center;
        border-radius: 50%;
        background-color: #e0e0e0;
        font-weight: bold;
        color: #6c757d;
        border: 5px dashed #ccc;
        margin: auto;
        cursor: pointer;
    }

    .edu-card {
        background-color: #f8f9fa;
        transition: 0.3s ease;
    }

        .edu-card:hover {
            background-color: #e9ecef;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
</style>