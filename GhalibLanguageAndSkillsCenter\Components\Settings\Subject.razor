﻿@page "/subjects"
@inject IGenericRepository<SubjectModel> subjectRepo
@inject IGenericRepository<ProgramModel> programRepo
@inject IGenericRepository<StaffModel> staffRepo
@rendermode InteractiveServer
@attribute [Authorize]

<h3>Subjects</h3>

@if (!string.IsNullOrEmpty(messages.Message))
{
    <SnackbarMessages Message="@messages.Message" Type="@messages.Type" />
}


<EditForm Model="@subject" OnValidSubmit="HandleSubmit">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label>Subject Name</label>
        <InputText class="form-control" @bind-Value="subject.Name" />
    </div>

    <div class="form-group">
        <label>Program</label>


        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectProgram SelectedProgramId="@subject.ProgramId"
        SelectedProgramIdChanged="OnProgramChange">

        </GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectProgram>

    </div>

    <div class="form-group">
        <label>Staff</label>
        <select class="form-control" @bind="subject.StaffId">
            <option value="">-- Select Staff --</option>
            @foreach (var staff in staffs)
            {
                <option value="@staff.StaffId">@staff.Name @staff.LastName</option>
            }
        </select>
    </div>

    <button class="btn btn-primary mt-2" type="submit">@((isEditMode ? "Update" : "Add") + " Subject")</button>
    @if (isEditMode)
    {
        <button type="button" class="btn btn-secondary mt-2 ms-2" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

<hr />
<h4>Subject List</h4>

@if (subjects.Any())
{
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Subject</th>
                <th>Program</th>
                <th>Teacher</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var s in subjects)
            {
                <tr>
                    <td>@s.Name</td>
                    <td>@s.ProgramName</td>
                    <td>@s.StaffFullName</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditSubject(s)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ShowDeleteModal(s.SubjectId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No subjects found.</p>
}
<DeleteModal IsVisible="@showModal" IsVisibleChanged="@((value)=>showModal = value)" OnDeleteConfirmed="@onDeleteConfirm" />

@code {
    private List<SubjectModel> subjects = new();
    private SubjectModel subject = new();
    private bool showModal = false;
    private List<ProgramModel> programs = new();
    private List<StaffModel> staffs = new();
    private bool isEditMode = false;
	private Messages messages = new();
    private int subjectIdToDelete;

    protected override async Task OnInitializedAsync()
    {
        await LoadAll();
    }
    private async Task onDeleteConfirm(bool confirmed)
    {
        if (confirmed)
        {
            await ConfirmDelete(subjectIdToDelete);
            showModal = false;
        }
        else{
			showModal = false;
        }
	}

    private async Task LoadAll()
    {
        subjects = (await subjectRepo.GetAllAsync("GetAllSubjects")).ToList();
        programs = (await programRepo.GetAllAsync("GetAllPrograms")).ToList();
        staffs = (await staffRepo.GetAllAsync("GetAllTeachers")).ToList();
    }

    private void OnProgramChange(int id)
    {
        subject.ProgramId = id;
    }
    private async Task HandleSubmit()
    {
        if (isEditMode)
        {
            await subjectRepo.UpdateAsync("UpdateSubject", new
            {
                subject.SubjectId,
                subject.Name,
                subject.ProgramId,
                subject.StaffId
            });
			messages.SetMessages("Subject updated successfully.", "Success");
			
        }
        else
        {
            await subjectRepo.AddAsync("AddSubject", new
            {
                subject.Name,
                subject.ProgramId,
                subject.StaffId
            });
			messages.SetMessages("Subject added successfully.", "Success"); 
        }

        subject = new();
        isEditMode = false;
        await LoadAll();
    }

    private void EditSubject(SubjectModel s)
    {
        isEditMode = true;
        subject = new SubjectModel
            {
                SubjectId = s.SubjectId,
                Name = s.Name,
                StaffId = s.StaffId,
                ProgramId = s.ProgramId
            };
    }
	private void ShowDeleteModal(int id)
	{
		subjectIdToDelete = id;
		showModal = true;
	}

    private async Task ConfirmDelete(int id)
    {
       
            await subjectRepo.DeleteAsync("DeleteSubject", new { SubjectId = id });
		messages.SetMessages("Subject deleted successfully.", "Success"); 
            await LoadAll();
        
    }

    private void CancelEdit()
    {
        subject = new();
        isEditMode = false;
    }

}
