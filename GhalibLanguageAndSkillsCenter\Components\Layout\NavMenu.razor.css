/* Modern Sidebar Mobile Toggle */
.navbar-toggler {
    appearance: none;
    cursor: pointer;
    width: 3rem;
    height: 2.5rem;
    color: white;
    position: absolute;
    top: 0.75rem;
    right: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") no-repeat center/1.5rem;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.navbar-toggler:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.navbar-toggler:checked {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Modern Header */
.top-row {
    min-height: 4rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.top-row::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    pointer-events: none;
}

.navbar-brand {
    font-size: 1.1rem;
    font-weight: 600;
    color: white !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
}

.bi {
    display: inline-block;
    position: relative;
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    top: -1px;
    background-size: cover;
}

.bi-house-door-fill-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-house-door-fill' viewBox='0 0 16 16'%3E%3Cpath d='M6.5 14.5v-3.505c0-.245.25-.495.5-.495h2c.25 0 .5.25.5.5v3.5a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.146-.354L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.354 1.146a.5.5 0 0 0-.708 0l-6 6A.5.5 0 0 0 1.5 7.5v7a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5Z'/%3E%3C/svg%3E");
}

.bi-plus-square-fill-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-plus-square-fill' viewBox='0 0 16 16'%3E%3Cpath d='M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm6.5 4.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3a.5.5 0 0 1 1 0z'/%3E%3C/svg%3E");
}

.bi-list-nested-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-list-nested' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M4.5 11.5A.5.5 0 0 1 5 11h10a.5.5 0 0 1 0 1H5a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 3 7h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 1 3h10a.5.5 0 0 1 0 1H1a.5.5 0 0 1-.5-.5z'/%3E%3C/svg%3E");
}

.bi-lock-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-list-nested' viewBox='0 0 16 16'%3E%3Cpath d='M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2zM5 8h6a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1z'/%3E%3C/svg%3E");
}

.bi-person-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-person' viewBox='0 0 16 16'%3E%3Cpath d='M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4Zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10Z'/%3E%3C/svg%3E");
}

.bi-person-badge-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-person-badge' viewBox='0 0 16 16'%3E%3Cpath d='M6.5 2a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1h-3zM11 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0z'/%3E%3Cpath d='M4.5 0A2.5 2.5 0 0 0 2 2.5V14a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2.5A2.5 2.5 0 0 0 11.5 0h-7zM3 2.5A1.5 1.5 0 0 1 4.5 1h7A1.5 1.5 0 0 1 13 2.5v10.795a4.2 4.2 0 0 0-.776-.492C11.392 12.387 10.063 12 8 12s-3.392.387-4.224.803a4.2 4.2 0 0 0-.776.492V2.5z'/%3E%3C/svg%3E");
}

.bi-person-fill-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-person-fill' viewBox='0 0 16 16'%3E%3Cpath d='M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3Zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z'/%3E%3C/svg%3E");
}

.bi-arrow-bar-left-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-arrow-bar-left' viewBox='0 0 16 16'%3E%3Cpath d='M12.5 15a.5.5 0 0 1-.5-.5v-13a.5.5 0 0 1 1 0v13a.5.5 0 0 1-.5.5ZM10 8a.5.5 0 0 1-.5.5H3.707l2.147 2.146a.5.5 0 0 1-.708.708l-3-3a.5.5 0 0 1 0-.708l3-3a.5.5 0 1 1 .708.708L3.707 7.5H9.5a.5.5 0 0 1 .5.5Z'/%3E%3C/svg%3E");
}

/* Modern Navigation Items */
.nav-item {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    position: relative;
}

.nav-item:first-of-type {
    margin-top: 1.5rem;
}

.nav-item:last-of-type {
    margin-bottom: 1.5rem;
}

/* Main Navigation Links */
.nav-item ::deep .nav-link {
    color: #64748b;
    background: none;
    border: none;
    border-radius: 12px;
    height: 3.2rem;
    display: flex;
    align-items: center;
    line-height: 3.2rem;
    width: calc(100% - 1rem);
    padding: 0.75rem 1rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    position: relative;
    overflow: hidden;
    text-decoration: none;
}

.nav-item ::deep .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 12px;
}

.nav-item ::deep .nav-link > * {
    position: relative;
    z-index: 1;
}

/* Active State */
.nav-item ::deep a.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transform: translateY(-1px);
}

.nav-item ::deep a.active::before {
    opacity: 1;
}

/* Hover State */
.nav-item ::deep .nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-item ::deep .nav-link:hover::before {
    opacity: 0.1;
}

/* Submenu Styling */
.nav-item ul {
    margin-left: 1rem;
    border-left: 2px solid rgba(102, 126, 234, 0.2);
    padding-left: 0.5rem;
}

.nav-item ul .nav-item ::deep .nav-link {
    height: 2.8rem;
    font-size: 0.85rem;
    color: #94a3b8;
    margin-left: 0.5rem;
    border-radius: 8px;
}

.nav-item ul .nav-item ::deep .nav-link:hover {
    background: rgba(102, 126, 234, 0.08);
    color: #667eea;
    transform: translateX(2px);
}

/* Scrollable Navigation */
.nav-scrollable {
    display: none;
    background: #f8fafc;
    border-radius: 0 0 20px 20px;
    margin-top: -1px;
}

.navbar-toggler:checked ~ .nav-scrollable {
    display: block;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Desktop Styles */
@media (min-width: 641px) {
    .navbar-toggler {
        display: none;
    }

    .nav-scrollable {
        display: block;
        background: #f8fafc;
        height: calc(100vh - 4rem);
        overflow-y: auto;
        overflow-x: hidden;
        border-radius: 0;
    }

    /* Custom Scrollbar */
    .nav-scrollable::-webkit-scrollbar {
        width: 6px;
    }

    .nav-scrollable::-webkit-scrollbar-track {
        background: transparent;
    }

    .nav-scrollable::-webkit-scrollbar-thumb {
        background: rgba(102, 126, 234, 0.3);
        border-radius: 3px;
    }

    .nav-scrollable::-webkit-scrollbar-thumb:hover {
        background: rgba(102, 126, 234, 0.5);
    }
}
/* Additional Animations */
.animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
}

/* Button Styling for Collapsible Items */
.nav-item button.nav-link {
    background: none !important;
    border: none !important;
    text-align: right;
    cursor: pointer;
}

.nav-item button.nav-link:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

/* Dropdown Arrow Animation */
.nav-item button .bi {
    transition: transform 0.2s ease;
}

/* Mobile Responsive */
@media (max-width: 640px) {
    .nav-scrollable {
        background: white;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        border-radius: 0 0 20px 20px;
        margin: 0 0.5rem;
    }

    .nav-item ::deep .nav-link {
        margin: 0.125rem 0.25rem;
        width: calc(100% - 0.5rem);
    }

    .top-row {
        min-height: 3.5rem;
    }

    .navbar-brand {
        font-size: 1rem;
    }
}

/* Icon Styling */
.nav-icon {
    width: 1.2rem;
    height: 1.2rem;
    margin-left: 0.75rem;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.nav-item ::deep .nav-link:hover .nav-icon,
.nav-item ::deep a.active .nav-icon {
    opacity: 1;
}

/* Page Container */
.page {
    background: #f8fafc;
}