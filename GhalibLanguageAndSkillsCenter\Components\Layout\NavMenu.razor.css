/* Ultra-Modern Sidebar - Complete Redesign */
.navbar-toggler {
    appearance: none;
    cursor: pointer;
    width: 3.5rem;
    height: 3rem;
    position: absolute;
    top: 0.5rem;
    right: 1rem;
    border: none;
    border-radius: 12px;
    background: linear-gradient(145deg, #667eea, #764ba2);
    box-shadow:
        0 4px 15px rgba(102, 126, 234, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.navbar-toggler::before {
    content: '';
    width: 20px;
    height: 2px;
    background: white;
    border-radius: 2px;
    box-shadow:
        0 6px 0 white,
        0 12px 0 white;
    transition: all 0.3s ease;
}

.navbar-toggler:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.navbar-toggler:checked::before {
    transform: rotate(45deg);
    box-shadow: 0 0 0 white;
}

.navbar-toggler:checked {
    background: linear-gradient(145deg, #f43f5e, #ec4899);
}

/* Revolutionary Header Design */
.top-row {
    min-height: 5rem;
    background:
        linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 75%, #475569 100%),
        radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.3) 0%, transparent 50%);
    border-bottom: 1px solid rgba(148, 163, 184, 0.3);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.top-row::before {
    content: '';
    position: absolute;
    top: 0;
    left: -50%;
    width: 200%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(59, 130, 246, 0.1) 25%,
        rgba(147, 51, 234, 0.1) 50%,
        rgba(236, 72, 153, 0.1) 75%,
        transparent 100%
    );
    animation: shimmer 3s ease-in-out infinite;
    pointer-events: none;
}

.top-row::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        #3b82f6 0%,
        #8b5cf6 25%,
        #ec4899 50%,
        #f59e0b 75%,
        #10b981 100%
    );
    opacity: 0.8;
}

.navbar-brand {
    font-size: 1.3rem;
    font-weight: 800;
    color: #ffffff !important;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(59, 130, 246, 0.3);
    position: relative;
    z-index: 2;
    letter-spacing: 1px;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.bi {
    display: inline-block;
    position: relative;
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    top: -1px;
    background-size: cover;
}

.bi-house-door-fill-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-house-door-fill' viewBox='0 0 16 16'%3E%3Cpath d='M6.5 14.5v-3.505c0-.245.25-.495.5-.495h2c.25 0 .5.25.5.5v3.5a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.146-.354L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.354 1.146a.5.5 0 0 0-.708 0l-6 6A.5.5 0 0 0 1.5 7.5v7a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5Z'/%3E%3C/svg%3E");
}

.bi-plus-square-fill-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-plus-square-fill' viewBox='0 0 16 16'%3E%3Cpath d='M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm6.5 4.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3a.5.5 0 0 1 1 0z'/%3E%3C/svg%3E");
}

.bi-list-nested-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-list-nested' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M4.5 11.5A.5.5 0 0 1 5 11h10a.5.5 0 0 1 0 1H5a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 3 7h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 1 3h10a.5.5 0 0 1 0 1H1a.5.5 0 0 1-.5-.5z'/%3E%3C/svg%3E");
}

.bi-lock-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-list-nested' viewBox='0 0 16 16'%3E%3Cpath d='M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2zM5 8h6a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1z'/%3E%3C/svg%3E");
}

.bi-person-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-person' viewBox='0 0 16 16'%3E%3Cpath d='M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4Zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10Z'/%3E%3C/svg%3E");
}

.bi-person-badge-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-person-badge' viewBox='0 0 16 16'%3E%3Cpath d='M6.5 2a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1h-3zM11 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0z'/%3E%3Cpath d='M4.5 0A2.5 2.5 0 0 0 2 2.5V14a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2.5A2.5 2.5 0 0 0 11.5 0h-7zM3 2.5A1.5 1.5 0 0 1 4.5 1h7A1.5 1.5 0 0 1 13 2.5v10.795a4.2 4.2 0 0 0-.776-.492C11.392 12.387 10.063 12 8 12s-3.392.387-4.224.803a4.2 4.2 0 0 0-.776.492V2.5z'/%3E%3C/svg%3E");
}

.bi-person-fill-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-person-fill' viewBox='0 0 16 16'%3E%3Cpath d='M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3Zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z'/%3E%3C/svg%3E");
}

.bi-arrow-bar-left-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-arrow-bar-left' viewBox='0 0 16 16'%3E%3Cpath d='M12.5 15a.5.5 0 0 1-.5-.5v-13a.5.5 0 0 1 1 0v13a.5.5 0 0 1-.5.5ZM10 8a.5.5 0 0 1-.5.5H3.707l2.147 2.146a.5.5 0 0 1-.708.708l-3-3a.5.5 0 0 1 0-.708l3-3a.5.5 0 1 1 .708.708L3.707 7.5H9.5a.5.5 0 0 1 .5.5Z'/%3E%3C/svg%3E");
}

/* Revolutionary Navigation Items */
.nav-item {
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
    position: relative;
}

.nav-item:first-of-type {
    margin-top: 2rem;
}

.nav-item:last-of-type {
    margin-bottom: 2rem;
}

/* Navigation Item Glow Effect */
.nav-item::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -10px;
    width: 4px;
    height: 0;
    background: linear-gradient(180deg, #3b82f6, #8b5cf6, #ec4899);
    border-radius: 2px;
    transform: translateY(-50%);
    transition: height 0.3s ease;
    opacity: 0;
}

.nav-item:hover::before {
    height: 60%;
    opacity: 1;
}

/* Revolutionary Navigation Links */
.nav-item ::deep .nav-link {
    color: #334155;
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%),
        linear-gradient(135deg, transparent 0%, rgba(59, 130, 246, 0.05) 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 20px;
    height: 4rem;
    display: flex;
    align-items: center;
    line-height: 4rem;
    width: calc(100% - 1.5rem);
    padding: 1rem 1.5rem;
    margin: 0.375rem 0.75rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(20px);
}

/* Advanced Link Effects */
.nav-item ::deep .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 20px;
    filter: blur(0px);
}

.nav-item ::deep .nav-link::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
}

.nav-item ::deep .nav-link > * {
    position: relative;
    z-index: 3;
    transition: all 0.3s ease;
}

/* Floating Animation */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

.nav-item ::deep .nav-link:hover > * {
    animation: float 2s ease-in-out infinite;
}

/* Revolutionary Active State */
.nav-item ::deep a.active {
    background:
        linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    color: #ffffff !important;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 12px 40px rgba(102, 126, 234, 0.6),
        0 8px 16px rgba(118, 75, 162, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    transform: translateY(-4px) scale(1.05);
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nav-item ::deep a.active::before {
    opacity: 1;
    filter: blur(2px);
}

.nav-item ::deep a.active::after {
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

.nav-item ::deep a.active .nav-icon {
    color: #ffffff !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
    transform: scale(1.1);
}

/* Active Item Pulse Animation */
.nav-item ::deep a.active {
    animation: activePulse 2s ease-in-out infinite;
}

@keyframes activePulse {
    0%, 100% {
        box-shadow:
            0 12px 40px rgba(102, 126, 234, 0.6),
            0 8px 16px rgba(118, 75, 162, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    50% {
        box-shadow:
            0 16px 50px rgba(102, 126, 234, 0.8),
            0 12px 24px rgba(118, 75, 162, 0.6),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
}

/* Revolutionary Hover State */
.nav-item ::deep .nav-link:hover {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 249, 255, 0.9) 100%),
        linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    color: #1e40af;
    transform: translateX(8px) translateY(-3px) scale(1.02);
    box-shadow:
        0 12px 35px rgba(59, 130, 246, 0.3),
        0 8px 16px rgba(139, 92, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border-color: rgba(59, 130, 246, 0.4);
    font-weight: 700;
}

.nav-item ::deep .nav-link:hover::before {
    opacity: 0.1;
    filter: blur(1px);
}

.nav-item ::deep .nav-link:hover::after {
    width: 120%;
    height: 120%;
    opacity: 0.2;
}

.nav-item ::deep .nav-link:hover .nav-icon {
    transform: scale(1.15) rotate(5deg);
    filter: drop-shadow(0 2px 8px rgba(59, 130, 246, 0.4));
}

/* Ultra Modern Submenu Styling */
.nav-item ul {
    margin-left: 1.5rem;
    border-left: 3px solid transparent;
    border-image: linear-gradient(180deg, #3b82f6, #8b5cf6) 1;
    padding-left: 1rem;
    position: relative;
}

.nav-item ul::before {
    content: '';
    position: absolute;
    left: -3px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #3b82f6 0%, #8b5cf6 100%);
    border-radius: 2px;
}

.nav-item ul .nav-item ::deep .nav-link {
    height: 3rem;
    font-size: 0.875rem;
    color: #64748b;
    margin-left: 0.5rem;
    border-radius: 12px;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border: 1px solid rgba(148, 163, 184, 0.15);
    font-weight: 500;
}

.nav-item ul .nav-item ::deep .nav-link:hover {
    background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
    color: #1e40af;
    transform: translateX(4px);
    border-color: rgba(59, 130, 246, 0.2);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.nav-item ul .nav-item ::deep a.active {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%);
    color: #ffffff !important;
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* Revolutionary Scrollable Navigation */
.nav-scrollable {
    display: none;
    background:
        linear-gradient(180deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%),
        radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
    border-radius: 0 0 32px 32px;
    margin-top: -1px;
    position: relative;
    box-shadow:
        inset 0 10px 20px rgba(0, 0, 0, 0.05),
        0 -5px 20px rgba(0, 0, 0, 0.1);
}

.nav-scrollable::before {
    content: '';
    position: absolute;
    top: 0;
    left: 2rem;
    right: 2rem;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(59, 130, 246, 0.3) 25%,
        rgba(139, 92, 246, 0.3) 50%,
        rgba(236, 72, 153, 0.3) 75%,
        transparent 100%
    );
    border-radius: 1px;
}

.nav-scrollable::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    border-radius: 2px 2px 0 0;
}

.navbar-toggler:checked ~ .nav-scrollable {
    display: block;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Desktop Styles */
@media (min-width: 641px) {
    .navbar-toggler {
        display: none;
    }

    .nav-scrollable {
        display: block;
        background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
        height: calc(100vh - 4.5rem);
        overflow-y: auto;
        overflow-x: hidden;
        border-radius: 0;
        position: relative;
    }

    .nav-scrollable::before {
        content: '';
        position: absolute;
        top: 0;
        left: 1rem;
        right: 1rem;
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, rgba(148, 163, 184, 0.3) 50%, transparent 100%);
    }

    /* Custom Scrollbar */
    .nav-scrollable::-webkit-scrollbar {
        width: 6px;
    }

    .nav-scrollable::-webkit-scrollbar-track {
        background: transparent;
    }

    .nav-scrollable::-webkit-scrollbar-thumb {
        background: rgba(102, 126, 234, 0.3);
        border-radius: 3px;
    }

    .nav-scrollable::-webkit-scrollbar-thumb:hover {
        background: rgba(102, 126, 234, 0.5);
    }
}
/* Additional Animations */
.animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
}

/* Button Styling for Collapsible Items */
.nav-item button.nav-link {
    background: none !important;
    border: none !important;
    text-align: right;
    cursor: pointer;
}

.nav-item button.nav-link:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

/* Dropdown Arrow Animation */
.nav-item button .bi {
    transition: transform 0.2s ease;
}

/* Mobile Responsive */
@media (max-width: 640px) {
    .nav-scrollable {
        background: white;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        border-radius: 0 0 20px 20px;
        margin: 0 0.5rem;
    }

    .nav-item ::deep .nav-link {
        margin: 0.125rem 0.25rem;
        width: calc(100% - 0.5rem);
    }

    .top-row {
        min-height: 3.5rem;
    }

    .navbar-brand {
        font-size: 1rem;
    }
}

/* Icon Styling */
.nav-icon {
    width: 1.2rem;
    height: 1.2rem;
    margin-left: 0.75rem;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.nav-item ::deep .nav-link:hover .nav-icon,
.nav-item ::deep a.active .nav-icon {
    opacity: 1;
}

/* Page Container */
.page {
    background: #f8fafc;
}