﻿@page "/users"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@inject UserManager<ApplicationUser> UserManager
@inject NavigationManager Navigation
@inject IJSRuntime JS
@rendermode InteractiveServer
<h3>User Management</h3>
@attribute [Authorize(Roles = "Administrator")]
@if (!string.IsNullOrEmpty(messages.Message))
{
    <SnackbarMessages Message="@messages.Message" Type="@messages.Type" />
}

@if (Users == null)
{
    <p><em>Loading users...</em></p>
}
else
{
    <table class="table table-bordered">
        <thead class="table-light">
            <tr>
                <th>Email</th>
                <th>New Email</th>
                <th>New Password</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var user in Users)
            {
                <tr>
                    <td>@user.Email</td>
                    <td>
                        <input class="form-control" @bind="user.NewEmail" />
                    </td>
                    <td>
                        <input class="form-control" @bind="user.NewPassword" type="password" />
                    </td>
                    <td>
                        <button class="btn btn-sm btn-primary me-2" @onclick="() => UpdateUser(user)">Update</button>
                        <button class="btn btn-sm btn-danger" @onclick="() => ShowDeleteModal(user.Id)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
<DeleteModal IsVisible="@showModal" IsVisibleChanged="@((value)=>showModal = value)" OnDeleteConfirmed="@onDeleteConfirm" />

@code {
    // Editable user view model for binding
    public class EditableUser
    {
        public string Id { get; set; }
        public string Email { get; set; }
        public string NewEmail { get; set; }
        public string NewPassword { get; set; }
    }
	private bool showModal = false;
	private Messages messages = new Messages();
	private string userIdToDelete;
    List<EditableUser> Users;
 
    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

	private async Task onDeleteConfirm(bool confirmed)
	{
		if (confirmed)
		{
			await DeleteUser(userIdToDelete);
			showModal = false;
		}
		else
		{
			showModal = false;
		}
	}
    
    private async Task LoadUsers()
    {
        var userList = await UserManager.Users.ToListAsync();
        Users = userList.Select(u => new EditableUser
            {
                Id = u.Id,
                Email = u.Email,
                NewEmail = u.Email
            }).ToList();
    }

    private async Task UpdateUser(EditableUser user)
    {
        var identityUser = await UserManager.FindByIdAsync(user.Id);
        if (identityUser == null)
        {
			messages.SetMessages("User not found.", "Error");
            return;
        }

        bool updated = false;

        if (user.NewEmail != identityUser.Email)
        {
            identityUser.Email = user.NewEmail;
            identityUser.UserName = user.NewEmail;
            var result = await UserManager.UpdateAsync(identityUser);
            if (!result.Succeeded)
            {
				messages.SetMessages("Failed to update email: " + string.Join(", ", result.Errors.Select(e => e.Description)), "Error");
                return;
            }
            updated = true;
        }

        if (!string.IsNullOrWhiteSpace(user.NewPassword))
        {
            var token = await UserManager.GeneratePasswordResetTokenAsync(identityUser);
            var passResult = await UserManager.ResetPasswordAsync(identityUser, token, user.NewPassword);
            if (!passResult.Succeeded)
            {
				messages.SetMessages("Failed to update password: " + string.Join(", ", passResult.Errors.Select(e => e.Description)), "Error");
                return;
            }
            updated = true;
        }

        if (updated)
        {
			messages.SetMessages("User updated successfully.", "Success");
        }
        else
        {
			messages.SetMessages("No changes made to user.", "Error");
        }

        await LoadUsers(); // reload updated data
    }
	private void ShowDeleteModal(string userId)
	{
		userIdToDelete = userId;
		showModal = true;
	}

    private async Task DeleteUser(string userId)
    {
       

        var user = await UserManager.FindByIdAsync(userId);
        if (user != null)
        {
            var result = await UserManager.DeleteAsync(user);
            if (result.Succeeded)
            {
				messages.SetMessages("User deleted successfully.", "Success");
                await LoadUsers();
            }
            else
            {
				messages.SetMessages("Failed to delete user: " + string.Join(", ", result.Errors.Select(e => e.Description)), "Error");
            }
        }
    }

   
}
