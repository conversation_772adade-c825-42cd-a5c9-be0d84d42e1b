﻿@using Blazorise.Snackbar

<Snackbar @ref="snackbarSuccess"
          Color="SnackbarColor.Success"
          AutoHideTimeout="5000"
          Class="custom-snackbar success-snackbar">
    <SnackbarBody Class="d-flex align-items-center">
        <span class="me-2 fs-4">
            <i class="bi bi-check-circle-fill"></i>
        </span>
        <div class="flex-grow-1">
            @Message
        </div>
        <SnackbarAction Clicked="@(() => snackbarSuccess.Hide())" Class="ms-3 btn-close-icon">
            <i class="bi bi-x-lg"></i>
        </SnackbarAction>
    </SnackbarBody>
</Snackbar>

<Snackbar @ref="snackbarError"
          Color="SnackbarColor.Danger"
          AutoHideTimeout="7000"
          Class="custom-snackbar error-snackbar">
    <SnackbarBody Class="d-flex align-items-center">
        <span class="me-2 fs-4">
            <i class="bi bi-exclamation-triangle-fill"></i>
        </span>
        <div class="flex-grow-1">
            @Message
        </div>
        <SnackbarAction Clicked="@(() => snackbarError.Hide())" Class="ms-3 btn-close-icon">
            <i class="bi bi-x-lg"></i>
        </SnackbarAction>
    </SnackbarBody>
</Snackbar>

@code {
    [Parameter]
    public string Message { get; set; } = "پیام پیش‌فرض";

    [Parameter]
    public string Type { get; set; }

    private string _lastMessage;
    private string _lastType;

    private Snackbar snackbarSuccess;
    private Snackbar snackbarError;
    protected override void OnAfterRender(bool firstRender)
    {
        // Do NOT call base.OnInitialized() here — this is OnAfterRender, not OnInitialized.

        if (firstRender)
        {
            if (Type == "Success" && snackbarSuccess is not null)
            {
                snackbarSuccess.Show();
            }
            else if (Type == "Error" && snackbarError is not null)
            {
                snackbarError.Show();
            }
        }
    }
    protected override void OnParametersSet()
    {
        if (!string.IsNullOrEmpty(Message) && (Message != _lastMessage || Type != _lastType))
        {
            _lastMessage = Message;
            _lastType = Type;

            // Show the appropriate snackbar
            if (Type == "Success" && snackbarSuccess is not null)
            {
                snackbarSuccess.Show();
            }
            else if (Type == "Error" && snackbarError is not null)
            {
                snackbarError.Show();
            }
        }
    }
}
