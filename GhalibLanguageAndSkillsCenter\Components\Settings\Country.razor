﻿@page "/country"
@using Dapper
@using Microsoft.Data.SqlClient
@using System.ComponentModel.DataAnnotations
@using System.Data
@inject IConfiguration Configuration
@rendermode InteractiveServer
@attribute [Authorize]
@if (!string.IsNullOrEmpty(messages.Message))
{

    <SnackbarMessages Message="@messages.Message" Type="@messages.Type" />
}
<h3>Country</h3>

<EditForm Model="@newCountry" OnValidSubmit="AddOrUpdateCountry" FormName="country">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label for="englishName">English Name</label>
        <InputText id="englishName" class="form-control" @bind-Value="newCountry.EnglishName" />
    </div>

    <div class="form-group">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newCountry.CountryPashto" />
    </div>

    <button class="btn btn-primary mt-2" type="submit">
        @if (isEditMode)
        {
            <text>Update Country</text>
        }
        else
        {
            <text>Add Country</text>
        }
    </button>

    @if (isEditMode)
    {
        <button class="btn btn-secondary mt-2 ms-2" type="button" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>



<hr />

<h3>Countries List</h3>

@if (countries is not null && countries.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English Name</th>
                <th>Pashto Name</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var country in countries)
            {
                <tr>
                    <td>@country.CountryId</td>
                    <td>@country.EnglishName</td>
                    <td>@country.CountryPashto</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditCountry(country)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDeleteCountry(country.CountryId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No countries available.</p>
}

<DeleteModal IsVisible="@showModal" IsVisibleChanged="@((value)=>showModal = value)" OnDeleteConfirmed="@onDeleteConfirm" />

@code {
    private CountryModel newCountry = new CountryModel();
    private List<CountryModel> countries = new List<CountryModel>();

    private Messages messages = new Messages();
    private bool showModal = false;

    private bool isEditMode = false;
    private bool isDeleteConfirmationVisible = false;
    private int countryIdToDelete;

    private string connectionString;

    protected override void OnInitialized()
    {
        connectionString = Configuration.GetConnectionString("DefaultConnection");
    }
    private async Task onDeleteConfirm(bool confirmed)
    {
        if(confirmed)
        {
            await DeleteCountry();
            showModal = false;
        }
        else
        {
            showModal = false;
        }
    }
    protected override async Task OnInitializedAsync()
    {
        await LoadCountries();
    }

    private async Task LoadCountries()
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            var result = await connection.QueryAsync<CountryModel>(
                "usp_GetAllCountries",
                commandType: CommandType.StoredProcedure);
            countries = result.ToList();
        }
        catch (Exception ex)
        {

            messages.Message = $"Error loading countries: {ex.Message}";
            messages.Type = "Error";
        }
    }

    private async Task AddOrUpdateCountry()
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            var parameters = new DynamicParameters();

            if (isEditMode)
            {
                parameters.Add("@CountryId", newCountry.CountryId, DbType.Byte);
                parameters.Add("@Country", newCountry.EnglishName, DbType.String);
                parameters.Add("@CountryPashto", newCountry.CountryPashto, DbType.String);

                await connection.ExecuteAsync(
                    "usp_UpdateCountry",
                    parameters,
                    commandType: CommandType.StoredProcedure);
            }
            else
            {
                parameters.Add("@Country", newCountry.EnglishName, DbType.String);
                parameters.Add("@CountryPashto", newCountry.CountryPashto, DbType.String);
                parameters.Add("@NewCountryId", dbType: DbType.Byte, direction: ParameterDirection.Output);

                await connection.ExecuteAsync(
                    "usp_CreateCountry",
                    parameters,
                    commandType: CommandType.StoredProcedure);

                // Optionally capture the new ID
                newCountry.CountryId = parameters.Get<byte>("@NewCountryId");
            }

            messages.Message = "Country saved successfully."; 

            newCountry = new CountryModel();
            isEditMode = false;

            await LoadCountries();
        }
        catch (Exception ex)
        {

            messages.Message = $"An error occurred: {ex.Message}";
            messages.Type = "Error";
        }
    }

    private void EditCountry(CountryModel country)
    {
        isEditMode = true;
        newCountry = new CountryModel
        {
            CountryId = country.CountryId,
            EnglishName = country.EnglishName,
            CountryPashto = country.CountryPashto
        };
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newCountry = new CountryModel();
    }

    private void ConfirmDeleteCountry(int countryId)
    {
        countryIdToDelete = countryId;
        showModal = true;
    }

    private async Task DeleteCountry()
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            var parameters = new DynamicParameters();
            parameters.Add("@CountryId", countryIdToDelete, DbType.Byte);

            await connection.ExecuteAsync(
                "usp_DeleteCountry",
                parameters,
                commandType: CommandType.StoredProcedure);

            await LoadCountries();

			messages.Message = "Country deleted successfully.";
			messages.Type = "Success";
            isDeleteConfirmationVisible = false;
        }
        catch (Exception ex)
        {
            
            messages.Message = $"An error occurred: {ex.Message}";
			messages.Type = "Error";
            isDeleteConfirmationVisible = false;
        }
    }

    

    public class CountryModel
    {
        public byte CountryId { get; set; }
        [Required(ErrorMessage = "Please enter a English Name.")]
        public string EnglishName { get; set; }
        [Required(ErrorMessage = "Please enter a Pashto Name.")]
        public string CountryPashto { get; set; }
    }
}
