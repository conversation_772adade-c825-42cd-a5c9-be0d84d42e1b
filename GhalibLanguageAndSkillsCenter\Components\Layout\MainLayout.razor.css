.page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

main {
    flex: 1;
    background: #ffffff;
    border-radius: 24px 0 0 0;
    box-shadow: -4px 0 30px rgba(0, 0, 0, 0.12);
    position: relative;
    z-index: 1;
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-right: none;
    border-bottom: none;
}

.sidebar {
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    box-shadow: 4px 0 30px rgba(0, 0, 0, 0.12);
    border-right: 1px solid rgba(148, 163, 184, 0.3);
    position: relative;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(180deg, rgba(59, 130, 246, 0.3) 0%, rgba(139, 92, 246, 0.3) 50%, rgba(236, 72, 153, 0.3) 100%);
}

.top-row {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    justify-content: flex-end;
    height: 4rem;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05);
}

    .top-row ::deep a, .top-row ::deep .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row ::deep a:hover, .top-row ::deep .btn-link:hover {
        text-decoration: underline;
    }

    .top-row ::deep a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row {
        justify-content: space-between;
    }

    .top-row ::deep a, .top-row ::deep .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page {
        flex-direction: row;
        background: #f1f5f9;
    }

    .sidebar {
        width: 320px;
        height: 100vh;
        position: sticky;
        top: 0;
        background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
        box-shadow: 4px 0 35px rgba(0, 0, 0, 0.12);
        border-right: 1px solid rgba(148, 163, 184, 0.3);
        z-index: 10;
    }

    .sidebar::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 1px;
        height: 100%;
        background: linear-gradient(180deg, rgba(59, 130, 246, 0.4) 0%, rgba(139, 92, 246, 0.4) 50%, rgba(236, 72, 153, 0.4) 100%);
    }

    main {
        border-radius: 28px 0 0 0;
        margin-left: -8px;
        background: #ffffff;
        box-shadow: -8px 0 35px rgba(0, 0, 0, 0.12);
        border: 1px solid rgba(226, 232, 240, 0.6);
        border-right: none;
        border-bottom: none;
        position: relative;
        z-index: 5;
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 15;
        border-radius: 28px 0 0 0;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(148, 163, 184, 0.2);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    }

    .top-row.auth ::deep a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row, article {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    article {
        background: #ffffff;
        border-radius: 0;
        margin-top: -1px;
    }
}

#blazor-error-ui {
    color-scheme: light only;
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
