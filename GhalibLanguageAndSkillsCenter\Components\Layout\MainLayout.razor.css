/* Sophisticated Page Layout */

.page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
}

main {
    flex: 1;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 24px 0 0 0;
    box-shadow:
        -4px 0 25px rgba(0, 0, 0, 0.08),
        -2px 0 10px rgba(0, 0, 0, 0.04);
    position: relative;
    z-index: 5;
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-right: none;
    border-bottom: none;
}

.sidebar {
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    border-right: 1px solid rgba(226, 232, 240, 0.6);
    position: relative;
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.05);
}

.top-row {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    justify-content: flex-end;
    height: 4.5rem;
    display: flex;
    align-items: center;
    backdrop-filter: blur(20px);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    border-radius: 24px 0 0 0;
    position: relative;
    z-index: 10;
}

    .top-row ::deep a, .top-row ::deep .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row ::deep a:hover, .top-row ::deep .btn-link:hover {
        text-decoration: underline;
    }

    .top-row ::deep a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row {
        justify-content: space-between;
    }

    .top-row ::deep a, .top-row ::deep .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page {
        flex-direction: row;
        background: #f1f5f9;
    }

    .sidebar {
        width: 320px;
        height: 100vh;
        position: sticky;
        top: 0;
        background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
        border-right: 1px solid rgba(226, 232, 240, 0.6);
        z-index: 15;
        box-shadow: 4px 0 25px rgba(0, 0, 0, 0.08);
    }

    main {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 28px 0 0 0;
        margin-left: -4px;
        position: relative;
        z-index: 10;
        box-shadow:
            -6px 0 25px rgba(0, 0, 0, 0.08),
            -2px 0 10px rgba(0, 0, 0, 0.04);
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 15;
        border-radius: 28px 0 0 0;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(148, 163, 184, 0.2);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    }

    .top-row.auth ::deep a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row, article {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    article {
        background: #ffffff;
        border-radius: 0;
        margin-top: -1px;
    }
}

#blazor-error-ui {
    color-scheme: light only;
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
