.page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: #f8fafc;
}

main {
    flex: 1;
    background: #ffffff;
    border-radius: 20px 0 0 0;
    box-shadow: -2px 0 20px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.sidebar {
    background: #ffffff;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
    border-right: 1px solid rgba(226, 232, 240, 0.8);
}

.top-row {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    justify-content: flex-end;
    height: 4rem;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05);
}

    .top-row ::deep a, .top-row ::deep .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row ::deep a:hover, .top-row ::deep .btn-link:hover {
        text-decoration: underline;
    }

    .top-row ::deep a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row {
        justify-content: space-between;
    }

    .top-row ::deep a, .top-row ::deep .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page {
        flex-direction: row;
        background: #f1f5f9;
    }

    .sidebar {
        width: 280px;
        height: 100vh;
        position: sticky;
        top: 0;
        background: #ffffff;
        box-shadow: 2px 0 25px rgba(0, 0, 0, 0.08);
        border-right: 1px solid rgba(226, 232, 240, 0.6);
    }

    main {
        border-radius: 25px 0 0 0;
        margin-left: -5px;
        background: #ffffff;
        box-shadow: -5px 0 25px rgba(0, 0, 0, 0.08);
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 10;
        border-radius: 25px 0 0 0;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        backdrop-filter: blur(15px);
    }

    .top-row.auth ::deep a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row, article {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    article {
        background: #ffffff;
        border-radius: 0;
        margin-top: -1px;
    }
}

#blazor-error-ui {
    color-scheme: light only;
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
