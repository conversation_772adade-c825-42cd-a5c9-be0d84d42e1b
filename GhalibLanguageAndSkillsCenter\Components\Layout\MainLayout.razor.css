/* Revolutionary Page Layout */
.page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background:
        linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 75%, #475569 100%),
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
    overflow-x: hidden;
}

main {
    flex: 1;
    background:
        linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%),
        radial-gradient(circle at 100% 0%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
    border-radius: 32px 0 0 0;
    box-shadow:
        -8px 0 40px rgba(0, 0, 0, 0.2),
        -4px 0 20px rgba(0, 0, 0, 0.1),
        inset 1px 1px 0 rgba(255, 255, 255, 0.8);
    position: relative;
    z-index: 5;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-right: none;
    border-bottom: none;
    backdrop-filter: blur(20px);
}

.sidebar {
    background:
        linear-gradient(180deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%),
        radial-gradient(circle at 0% 50%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
    box-shadow:
        8px 0 40px rgba(0, 0, 0, 0.15),
        4px 0 20px rgba(0, 0, 0, 0.1),
        inset -1px 0 0 rgba(255, 255, 255, 0.6);
    border-right: 1px solid rgba(148, 163, 184, 0.2);
    position: relative;
    backdrop-filter: blur(20px);
    z-index: 10;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(180deg,
        rgba(59, 130, 246, 0.6) 0%,
        rgba(139, 92, 246, 0.6) 25%,
        rgba(236, 72, 153, 0.6) 50%,
        rgba(245, 158, 11, 0.6) 75%,
        rgba(16, 185, 129, 0.6) 100%
    );
    border-radius: 1px;
    animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

.top-row {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    justify-content: flex-end;
    height: 4rem;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05);
}

    .top-row ::deep a, .top-row ::deep .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row ::deep a:hover, .top-row ::deep .btn-link:hover {
        text-decoration: underline;
    }

    .top-row ::deep a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row {
        justify-content: space-between;
    }

    .top-row ::deep a, .top-row ::deep .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page {
        flex-direction: row;
        background: #f1f5f9;
    }

    .sidebar {
        width: 360px;
        height: 100vh;
        position: sticky;
        top: 0;
        background:
            linear-gradient(180deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%),
            radial-gradient(circle at 0% 50%, rgba(59, 130, 246, 0.08) 0%, transparent 50%);
        box-shadow:
            8px 0 50px rgba(0, 0, 0, 0.2),
            4px 0 25px rgba(0, 0, 0, 0.1),
            inset -1px 0 0 rgba(255, 255, 255, 0.8);
        border-right: 1px solid rgba(148, 163, 184, 0.2);
        z-index: 15;
        backdrop-filter: blur(25px);
    }

    .sidebar::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 3px;
        height: 100%;
        background: linear-gradient(180deg,
            rgba(59, 130, 246, 0.8) 0%,
            rgba(139, 92, 246, 0.8) 20%,
            rgba(236, 72, 153, 0.8) 40%,
            rgba(245, 158, 11, 0.8) 60%,
            rgba(16, 185, 129, 0.8) 80%,
            rgba(59, 130, 246, 0.8) 100%
        );
        border-radius: 2px;
        animation: borderGlow 4s ease-in-out infinite;
    }

    main {
        border-radius: 36px 0 0 0;
        margin-left: -12px;
        background:
            linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%),
            radial-gradient(circle at 100% 0%, rgba(59, 130, 246, 0.08) 0%, transparent 50%);
        box-shadow:
            -12px 0 50px rgba(0, 0, 0, 0.2),
            -6px 0 25px rgba(0, 0, 0, 0.1),
            inset 1px 1px 0 rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-right: none;
        border-bottom: none;
        position: relative;
        z-index: 10;
        backdrop-filter: blur(25px);
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 15;
        border-radius: 28px 0 0 0;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(148, 163, 184, 0.2);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    }

    .top-row.auth ::deep a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row, article {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    article {
        background: #ffffff;
        border-radius: 0;
        margin-top: -1px;
    }
}

#blazor-error-ui {
    color-scheme: light only;
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
