﻿@page "/department"
@inject IGenericRepository<DepartmentModel> DepartmentRepo
@inject IJSRuntime JS
@rendermode InteractiveServer
@attribute [Authorize]

@if (!string.IsNullOrEmpty(messages.Message))
{
    <SnackbarMessages Message="@messages.Message" Type="@messages.Type" />
}

<h3>Add Department</h3>

<EditForm Model="@newDepartment" OnValidSubmit="AddOrUpdateDepartment" FormName="department">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label for="englishName">English Name</label>
        <InputText id="englishName" class="form-control" @bind-Value="newDepartment.EnglishName" />
    </div>

    <div class="form-group">
        <label for="dariName">Dari Name</label>
        <InputText id="dariName" class="form-control" @bind-Value="newDepartment.DariName" />
    </div>

    <div class="form-group">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newDepartment.PashtoName" />
    </div>

    <button class="btn btn-primary mt-2" type="submit">
        @if (isEditMode)
        {
            <text>Update Department</text>
        }
        else
        {
            <text>Add Department</text>
        }
    </button>

    @if (isEditMode)
    {
        <button class="btn btn-secondary mt-2 ms-2" type="button" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

<hr />

<h3>Departments List</h3>

@if (departments is not null && departments.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English Name</th>
                <th>Dari Name</th>
                <th>Pashto Name</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var dept in departments)
            {
                <tr>
                    <td>@dept.DepartmentId</td>
                    <td>@dept.EnglishName</td>
                    <td>@dept.DariName</td>
                    <td>@dept.PashtoName</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditDepartment(dept)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(dept.DepartmentId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No departments available.</p>
}
<DeleteModal IsVisible="@showModal" IsVisibleChanged="@((value)=>showModal = value)" OnDeleteConfirmed="@onDeleteConfirm" />


@code {
    private DepartmentModel newDepartment = new DepartmentModel();
    private List<DepartmentModel> departments = new List<DepartmentModel>();
    private bool showModal = false;
    private bool isEditMode = false;
    private Messages messages = new Messages();
    private int departmentIdToDelete;
    protected override async Task OnInitializedAsync()
    {
        await LoadDepartments();
    }
    private async Task onDeleteConfirm(bool confirm)
    {
        if(confirm)
        {

            await DeleteDepartment(departmentIdToDelete);
            showModal = false; // Hide the modal after deletion
        }
        else{
            showModal = false; // Hide the modal if deletion is cancelled
        }
    }
    private async Task LoadDepartments()
    {
        try
        {
            departments = (await DepartmentRepo.GetAllAsync("AllDepartments")).ToList();
        }
        catch (Exception ex)
        {
            messages.Message = $"Error loading departments: {ex.Message}";
            messages.Type = "Error";
        }
    }

    private async Task AddOrUpdateDepartment()
    {
        try
        {
            if (isEditMode)
            {
                await DepartmentRepo.UpdateAsync("UpdateDepartment", newDepartment);
                messages.Message = "Department updated successfully!";
                messages.Type = "Success";
            }
            else
            {
                await DepartmentRepo.AddAsync("AddDepartment", new
                {
                    EnglishName = newDepartment.EnglishName,
                    DariName = newDepartment.DariName,
                    PashtoName = newDepartment.PashtoName
                });
                messages.Message = "Department added successfully!";
                messages.Type = "Success";
            }

            newDepartment = new DepartmentModel();
            isEditMode = false;

            await LoadDepartments();
        }
        catch (Exception ex)
        {
            messages.Message = $"An error occurred: {ex.Message}";
            messages.Type = "Error";
        }
    }

    private void EditDepartment(DepartmentModel department)
    {
        isEditMode = true;
        newDepartment = new DepartmentModel
            {
                DepartmentId = department.DepartmentId,
                EnglishName = department.EnglishName,
                DariName = department.DariName,
                PashtoName = department.PashtoName
            };
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newDepartment = new DepartmentModel();
    }

    private async Task ConfirmDelete(int departmentId)
    {
        departmentIdToDelete = departmentId;
        showModal = true;
    }

    private async Task DeleteDepartment(int departmentId)
    {
        try
        {
            await DepartmentRepo.DeleteAsync("DeleteDepartment", new { DepartmentId = departmentId });
            await LoadDepartments();
            messages.Message = "Department deleted successfully!";
            messages.Type = "Success";
        }
        catch (Exception ex)
        {
            messages.Message = $"An error occurred: {ex.Message}";
            messages.Type = "Error";
        }
    }

   
}
