﻿@page "/Position"
@inject IGenericRepository<PositionModel> PositionRepo
@attribute [Authorize]
@rendermode InteractiveServer

<h3>@(isEditMode ? "Edit" : "Add") Position</h3>
@if (!string.IsNullOrEmpty(messages.Message))
{
    <SnackbarMessages Message="@messages.Message" Type="@messages.Type" />
}

<EditForm Model="@newPosition" OnValidSubmit="HandleValidSubmit" FormName="position">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label for="englishName">English Name</label>
        <InputText id="englishName" class="form-control" @bind-Value="newPosition.EnglishName" />
    </div>

    <div class="form-group">
        <label for="dariName">Dari Name</label>
        <InputText id="dariName" class="form-control" @bind-Value="newPosition.DariName" />
    </div>

    <div class="form-group">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newPosition.PashtoName" />
    </div>

    <button class="btn btn-primary mt-2" type="submit">
        @(isEditMode ? "Update Position" : "Add Position")
    </button>

   
</EditForm>

<hr />

<h3>Positions List</h3>

@if (positions != null && positions.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English Name</th>
                <th>Dari Name</th>
                <th>Pashto Name</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var pos in positions)
            {
                <tr>
                    <td>@pos.PositionId</td>
                    <td>@pos.EnglishName</td>
                    <td>@pos.DariName</td>
                    <td>@pos.PashtoName</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditPosition(pos)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(pos.PositionId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No positions available.</p>
}



<DeleteModal IsVisible="@showModal" IsVisibleChanged="@((value)=>showModal = value)" OnDeleteConfirmed="@onDeleteConfirm" />

@code {
    private PositionModel newPosition = new PositionModel();
    private IEnumerable<PositionModel> positions = new List<PositionModel>();
	private Messages messages = new Messages();
    private bool showModal = false;
    
    
    private bool isEditMode = false;

    private int positionIdToDelete;

    protected override async Task OnInitializedAsync()
    {
        await LoadPositions();
    }
    private async Task onDeleteConfirm(bool confirmed)
    {
        if (confirmed)
        {
            await DeletePosition();
            showModal = false;
        }
        else
        {
            showModal = false;
        }
	}
    private async Task LoadPositions()
    {
        try
        {
            positions = await PositionRepo.GetAllAsync("GetAllPositions");        }
        catch (Exception ex)
        {
			messages.SetMessages("Error loading positions: " + ex.Message, "Error");
        }
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            if (isEditMode)
            {
                // Update Position
                await PositionRepo.UpdateAsync("UpdatePosition", newPosition);
            }
            else
            {
                // Add Position
                await PositionRepo.AddAsync("AddPosition",new{EnglishName=newPosition.EnglishName,DariName = newPosition.DariName,PashtoName = newPosition.PashtoName});
            }

            // Reset after operation
			messages.SetMessages("Operation completed successfully!", "Success");
            newPosition = new PositionModel();
            isEditMode = false;

            await LoadPositions();
        }
        catch (Exception ex)
        {
			messages.SetMessages("An error occurred: " + ex.Message, "Error");
        }
    }

    private void EditPosition(PositionModel position)
    {
        isEditMode = true;
        newPosition = new PositionModel
            {
                PositionId = position.PositionId,
                EnglishName = position.EnglishName,
                DariName = position.DariName,
                PashtoName = position.PashtoName
            };
    }

    private void ConfirmDelete(int positionId)
    {
        // Show confirmation dialog
        positionIdToDelete = positionId;
        showModal = true;
    }

    private async Task DeletePosition()
    {
        try
        {
            await PositionRepo.DeleteAsync("DeletePosition", new { PositionId  = positionIdToDelete});
            await LoadPositions();
            showModal = false;  // Close confirmation dialog
			messages.SetMessages("Position deleted successfully.", "Success");

        }
        catch (Exception ex)
        {
			messages.SetMessages("An error occurred while deleting the position: " + ex.Message, "Error");
            showModal = false;  // Close confirmation dialog
        }
    }

   
}
